import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-black text-white py-8 px-6 md:px-12">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm mb-4 md:mb-0">COPYRIGHT © 2024 COURT ONE PADS. ALL RIGHTS RESERVED</p>
          
          <div className="flex space-x-6 mb-4 md:mb-0">
            {/* Social Media Icons */}
            <Link href="#" aria-label="Twitter">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
              </svg>
            </Link>
            <Link href="#" aria-label="Medium">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <path d="M8 12h8"></path>
                <path d="M12 8v8"></path>
              </svg>
            </Link>
            <Link href="#" aria-label="X">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </Link>
          </div>
        </div>
        
        <div className="flex flex-wrap justify-center md:justify-start gap-6 mt-6 text-sm">
          <Link href="#" className="hover:text-zinc-300">PRIVACY POLICY</Link>
          <Link href="#" className="hover:text-zinc-300">TERMS & CONDITIONS</Link>
          <Link href="#" className="hover:text-zinc-300">WHITEPAPER</Link>
        </div>
      </div>
    </footer>
  );
}
