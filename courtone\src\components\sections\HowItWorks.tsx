import { Card, CardContent } from "@/components/ui/card";

interface FeatureCardProps {
  number: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

function FeatureCard({ number, title, description, icon }: FeatureCardProps) {
  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardContent className="p-6">
        <div className="mb-4 text-lime-400">{icon}</div>
        <p className="text-lime-400 font-bold mb-2">{number}</p>
        <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
        <p className="text-zinc-400">{description}</p>
      </CardContent>
    </Card>
  );
}

export default function HowItWorks() {
  return (
    <section className="bg-black text-white py-20 px-6 md:px-12">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-3xl md:text-4xl font-bold text-lime-400 text-center mb-12">
          HOW DOES COURT ONE WORKS?
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <FeatureCard 
            number="01"
            title="YOU INVEST"
            description="Mint a CourtOne NFT to claim your ownership stake"
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
                <path d="M12 18h.01"></path>
                <path d="M12 6v8"></path>
              </svg>
            }
          />
          
          <FeatureCard 
            number="02"
            title="WE BUILD"
            description="Premium padel court in high-traffic areas in Indonesia"
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="2" y="12" width="6" height="8" rx="1"></rect>
                <rect x="9" y="8" width="6" height="12" rx="1"></rect>
                <rect x="16" y="4" width="6" height="16" rx="1"></rect>
              </svg>
            }
          />
          
          <FeatureCard 
            number="03"
            title="YOU EARN"
            description="Enjoy passive income with profits distributed monthly"
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 2v20"></path>
                <path d="m17 5-5-3-5 3"></path>
                <path d="m17 19-5 3-5-3"></path>
                <path d="M5 12H2"></path>
                <path d="M22 12h-3"></path>
              </svg>
            }
          />
          
          <FeatureCard 
            number="04"
            title="YOU PLAY"
            description="Get community perks and early access to special events"
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="m8 14 2.5-2.5"></path>
                <path d="M14 8v6"></path>
                <path d="M10 10v4"></path>
              </svg>
            }
          />
        </div>
      </div>
    </section>
  );
}
