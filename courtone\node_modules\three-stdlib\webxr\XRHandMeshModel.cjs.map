{"version": 3, "file": "XRHandMeshModel.cjs", "sources": ["../../src/webxr/XRHandMeshModel.ts"], "sourcesContent": ["import { Object3D } from 'three'\nimport { GLTFLoader } from '../loaders/GLTFLoader'\n\nconst DEFAULT_HAND_PROFILE_PATH =\n  'https://cdn.jsdelivr.net/npm/@webxr-input-profiles/assets@1.0/dist/profiles/generic-hand/'\n\nclass XRHandMeshModel {\n  controller: Object3D\n  handModel: Object3D\n  bones: Object3D[]\n\n  constructor(\n    handModel: Object3D,\n    controller: Object3D,\n    path: string = DEFAULT_HAND_PROFILE_PATH,\n    handedness: string,\n    customModelPath?: string,\n  ) {\n    this.controller = controller\n    this.handModel = handModel\n\n    this.bones = []\n\n    const loader = new GLTFLoader()\n    if (!customModelPath) loader.setPath(path)\n    loader.load(customModelPath ?? `${handedness}.glb`, (gltf: { scene: Object3D }) => {\n      const object = gltf.scene.children[0]\n      this.handModel.add(object)\n\n      const mesh = object.getObjectByProperty('type', 'SkinnedMesh')!\n      mesh.frustumCulled = false\n      mesh.castShadow = true\n      mesh.receiveShadow = true\n\n      const joints = [\n        'wrist',\n        'thumb-metacarpal',\n        'thumb-phalanx-proximal',\n        'thumb-phalanx-distal',\n        'thumb-tip',\n        'index-finger-metacarpal',\n        'index-finger-phalanx-proximal',\n        'index-finger-phalanx-intermediate',\n        'index-finger-phalanx-distal',\n        'index-finger-tip',\n        'middle-finger-metacarpal',\n        'middle-finger-phalanx-proximal',\n        'middle-finger-phalanx-intermediate',\n        'middle-finger-phalanx-distal',\n        'middle-finger-tip',\n        'ring-finger-metacarpal',\n        'ring-finger-phalanx-proximal',\n        'ring-finger-phalanx-intermediate',\n        'ring-finger-phalanx-distal',\n        'ring-finger-tip',\n        'pinky-finger-metacarpal',\n        'pinky-finger-phalanx-proximal',\n        'pinky-finger-phalanx-intermediate',\n        'pinky-finger-phalanx-distal',\n        'pinky-finger-tip',\n      ]\n\n      joints.forEach((jointName) => {\n        const bone = object.getObjectByName(jointName) as any\n\n        if (bone !== undefined) {\n          bone.jointName = jointName\n        } else {\n          console.warn(`Couldn't find ${jointName} in ${handedness} hand mesh`)\n        }\n\n        this.bones.push(bone)\n      })\n    })\n  }\n\n  updateMesh(): void {\n    // XR Joints\n    const XRJoints = (this.controller as any).joints\n\n    for (let i = 0; i < this.bones.length; i++) {\n      const bone = this.bones[i]\n\n      if (bone) {\n        const XRJoint = XRJoints[(bone as any).jointName]\n\n        if (XRJoint.visible) {\n          const position = XRJoint.position\n\n          bone.position.copy(position)\n          bone.quaternion.copy(XRJoint.quaternion)\n          // bone.scale.setScalar( XRJoint.jointRadius || defaultRadius );\n        }\n      }\n    }\n  }\n}\n\nexport { XRHandMeshModel }\n"], "names": ["GLTFLoader"], "mappings": ";;;;;;;;;AAGA,MAAM,4BACJ;AAEF,MAAM,gBAAgB;AAAA,EAKpB,YACE,WACA,YACA,OAAe,2BACf,YACA,iBACA;AAVF;AACA;AACA;AASE,SAAK,aAAa;AAClB,SAAK,YAAY;AAEjB,SAAK,QAAQ;AAEP,UAAA,SAAS,IAAIA,WAAAA;AACnB,QAAI,CAAC;AAAiB,aAAO,QAAQ,IAAI;AACzC,WAAO,KAAK,4CAAmB,GAAG,kBAAkB,CAAC,SAA8B;AACjF,YAAM,SAAS,KAAK,MAAM,SAAS,CAAC;AAC/B,WAAA,UAAU,IAAI,MAAM;AAEzB,YAAM,OAAO,OAAO,oBAAoB,QAAQ,aAAa;AAC7D,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAClB,WAAK,gBAAgB;AAErB,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAGK,aAAA,QAAQ,CAAC,cAAc;AACtB,cAAA,OAAO,OAAO,gBAAgB,SAAS;AAE7C,YAAI,SAAS,QAAW;AACtB,eAAK,YAAY;AAAA,QAAA,OACZ;AACG,kBAAA,KAAK,iBAAiB,gBAAgB,sBAAsB;AAAA,QACtE;AAEK,aAAA,MAAM,KAAK,IAAI;AAAA,MAAA,CACrB;AAAA,IAAA,CACF;AAAA,EACH;AAAA,EAEA,aAAmB;AAEX,UAAA,WAAY,KAAK,WAAmB;AAE1C,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACpC,YAAA,OAAO,KAAK,MAAM,CAAC;AAEzB,UAAI,MAAM;AACF,cAAA,UAAU,SAAU,KAAa,SAAS;AAEhD,YAAI,QAAQ,SAAS;AACnB,gBAAM,WAAW,QAAQ;AAEpB,eAAA,SAAS,KAAK,QAAQ;AACtB,eAAA,WAAW,KAAK,QAAQ,UAAU;AAAA,QAEzC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;"}