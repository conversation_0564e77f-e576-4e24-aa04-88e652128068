"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_3d_Interactive3DModel_tsx";
exports.ids = ["_ssr_src_components_3d_Interactive3DModel_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/3d/Interactive3DModel.tsx":
/*!**************************************************!*\
  !*** ./src/components/3d/Interactive3DModel.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Interactive3DModel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/Gltf.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/Environment.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Model() {\n    const { scene } = (0,_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF)('/scene.gltf');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n        object: scene,\n        scale: [\n            0.6,\n            0.6,\n            0.6\n        ],\n        position: [\n            0,\n            -0.5,\n            0\n        ],\n        rotation: [\n            0,\n            0,\n            0\n        ]\n    }, void 0, false, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingFallback() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                args: [\n                    1,\n                    1,\n                    1\n                ]\n            }, void 0, false, {\n                fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                color: \"#a3e635\"\n            }, void 0, false, {\n                fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\nfunction Interactive3DModel() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.Canvas, {\n            camera: {\n                position: [\n                    0,\n                    0,\n                    10\n                ],\n                fov: 40\n            },\n            style: {\n                background: 'transparent'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ambientLight\", {\n                    intensity: 0.5\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"directionalLight\", {\n                    position: [\n                        10,\n                        10,\n                        5\n                    ],\n                    intensity: 1\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pointLight\", {\n                    position: [\n                        -10,\n                        -10,\n                        -5\n                    ],\n                    intensity: 0.5\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingFallback, {}, void 0, false, {\n                        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 29\n                    }, void 0),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Model, {}, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.Environment, {\n                            preset: \"studio\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_5__.OrbitControls, {\n                    enablePan: true,\n                    enableZoom: true,\n                    enableRotate: true,\n                    autoRotate: true,\n                    autoRotateSpeed: 1,\n                    minDistance: 5,\n                    maxDistance: 20,\n                    target: [\n                        0,\n                        -0.5,\n                        0\n                    ]\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n// Preload the model\n_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF.preload('/scene.gltf');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/3d/Interactive3DModel.tsx\n");

/***/ })

};
;