"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_3d_Interactive3DModel_tsx";
exports.ids = ["_ssr_src_components_3d_Interactive3DModel_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/3d/Interactive3DModel.tsx":
/*!**************************************************!*\
  !*** ./src/components/3d/Interactive3DModel.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Interactive3DModel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/Gltf.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/Environment.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Model() {\n    const { scene } = (0,_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF)('/scene.gltf');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n        object: scene,\n        scale: [\n            0.8,\n            0.8,\n            0.8\n        ],\n        position: [\n            0,\n            0,\n            0\n        ],\n        rotation: [\n            0,\n            0,\n            0\n        ]\n    }, void 0, false, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingFallback() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                args: [\n                    1,\n                    1,\n                    1\n                ]\n            }, void 0, false, {\n                fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                color: \"#a3e635\"\n            }, void 0, false, {\n                fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\nfunction Interactive3DModel() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.Canvas, {\n            camera: {\n                position: [\n                    0,\n                    0,\n                    8\n                ],\n                fov: 45\n            },\n            style: {\n                background: 'transparent'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ambientLight\", {\n                    intensity: 0.5\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"directionalLight\", {\n                    position: [\n                        10,\n                        10,\n                        5\n                    ],\n                    intensity: 1\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pointLight\", {\n                    position: [\n                        -10,\n                        -10,\n                        -5\n                    ],\n                    intensity: 0.5\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingFallback, {}, void 0, false, {\n                        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 29\n                    }, void 0),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Model, {}, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.Environment, {\n                            preset: \"studio\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_5__.OrbitControls, {\n                    enablePan: true,\n                    enableZoom: true,\n                    enableRotate: true,\n                    autoRotate: true,\n                    autoRotateSpeed: 1,\n                    minDistance: 3,\n                    maxDistance: 15,\n                    target: [\n                        0,\n                        0,\n                        0\n                    ]\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n// Preload the model\n_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF.preload('/scene.gltf');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/3d/Interactive3DModel.tsx\n");

/***/ })

};
;