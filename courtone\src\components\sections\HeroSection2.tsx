export default function HeroSection2() {
  return (
    <section className="relative py-24 px-6 md:px-12 overflow-hidden">
      {/* Background image with overlay */}
      <div 
        className="absolute inset-0 bg-black/70 z-10"
        style={{
          backgroundImage: "url('/court-bw.jpg')",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundBlendMode: "overlay",
        }}
      />
      
      <div className="relative z-20 max-w-7xl mx-auto text-center text-white">
        <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
          REAL COURTS. REAL RETURNS. ON-CHAIN.
        </h2>
        
        <p className="text-lg md:text-xl max-w-3xl mx-auto">
          Court One is the first platform fully powered by blockchain technology that connects real-world padel court assets with digital ownership, creating a new paradigm for sports investment and community engagement.
        </p>
      </div>
    </section>
  );
}
