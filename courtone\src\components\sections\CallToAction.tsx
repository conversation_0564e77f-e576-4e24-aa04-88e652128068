import { Button } from "@/components/ui/button";

export default function CallToAction() {
  return (
    <section className="relative py-24 px-6 md:px-12 overflow-hidden">
      {/* Background image with overlay */}
      <div 
        className="absolute inset-0 bg-black/70 z-10"
        style={{
          backgroundImage: "url('/image2.png')",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundBlendMode: "overlay",
        }}
      />
      
      <div className="relative z-20 max-w-4xl mx-auto text-center text-white">
        <h2 className="text-4xl md:text-5xl font-bold mb-6">
          STAKE IN THE COURT. EARN LIKE AN OWNER.
        </h2>
        
        <p className="text-lg md:text-xl mb-10">
          Invest in sport, earn from real players, and be part of a world-first innovation
        </p>
        
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button className="bg-lime-500 text-black hover:bg-lime-400 font-semibold px-8 py-6">
            Buy your Stake
          </Button>
          <Button variant="outline" className="border-white text-white hover:bg-white hover:text-black font-semibold px-8 py-6">
            Whitepaper
          </Button>
        </div>
      </div>
    </section>
  );
}
