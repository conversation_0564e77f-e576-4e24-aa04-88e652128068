import Image from 'next/image';

interface FeatureItemProps {
  title: string;
  description: string;
}

function FeatureItem({ title, description }: FeatureItemProps) {
  return (
    <div className="flex mb-6">
      <div className="mr-4 text-lime-400">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M20 6 9 17l-5-5"></path>
        </svg>
      </div>
      <div>
        <h4 className="font-bold text-white mb-1">{title}</h4>
        <p className="text-zinc-400">{description}</p>
      </div>
    </div>
  );
}

export default function NftIncome() {
  return (
    <section className="bg-black text-white py-20 px-6 md:px-12">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row items-center">
          <div className="w-full md:w-1/2 mb-10 md:mb-0">
            <h2 className="text-3xl md:text-4xl font-bold text-lime-400 mb-4">
              YOUR NFT, YOUR INCOME
            </h2>
            
            <p className="text-xl mb-8">
              Earn up to 15% APR from real padel court revenue
            </p>
            
            <div>
              <FeatureItem 
                title="Protected APR"
                description="Up to 15% annually based on actual court rental and operational profits"
              />
              
              <FeatureItem 
                title="Revenue Sources"
                description="Court bookings, equipment rentals, private events, and future F&B"
              />
              
              <FeatureItem 
                title="On-Chain Reporting"
                description="Transparent earnings via our real-time dashboard"
              />
              
              <FeatureItem 
                title="Monthly Payouts"
                description="Distributed directly to NFT holders"
              />
            </div>
            
            <p className="text-xs text-zinc-500 mt-6">
              Returns are based on net profit and are subject to operational performance
            </p>
          </div>
          
          <div className="w-full md:w-1/2 flex justify-center">
            <div className="relative w-80 h-80">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative w-40 h-40">
                  <Image 
                    src="/tennis-ball.png" 
                    alt="Tennis Ball"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
              
              {/* NFT Icons around the ball */}
              <div className="absolute top-0 left-1/2 -translate-x-1/2 bg-zinc-800 p-3 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-lime-400">
                  <path d="M11 5.08V2a10 10 0 0 0-9 10h3.08A7 7 0 0 1 11 5.08z"></path>
                  <path d="M13 5.08V2a10 10 0 0 1 9 10h-3.08A7 7 0 0 0 13 5.08z"></path>
                  <path d="M11 18.92V22a10 10 0 0 1-9-10h3.08A7 7 0 0 0 11 18.92z"></path>
                  <path d="M13 18.92V22a10 10 0 0 0 9-10h-3.08A7 7 0 0 1 13 18.92z"></path>
                </svg>
              </div>
              
              <div className="absolute bottom-0 left-1/2 -translate-x-1/2 bg-zinc-800 p-3 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-lime-400">
                  <rect x="2" y="6" width="20" height="12" rx="2"></rect>
                  <circle cx="12" cy="12" r="2"></circle>
                  <path d="M6 12h.01M18 12h.01"></path>
                </svg>
              </div>
              
              <div className="absolute top-1/2 left-0 -translate-y-1/2 bg-zinc-800 p-3 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-lime-400">
                  <path d="M12 2v20"></path>
                  <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                </svg>
              </div>
              
              <div className="absolute top-1/2 right-0 -translate-y-1/2 bg-zinc-800 p-3 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-lime-400">
                  <path d="M20 12V8H6a2 2 0 0 1-2-2c0-1.1.9-2 2-2h12v4"></path>
                  <path d="M4 6v12c0 1.1.9 2 2 2h14v-4"></path>
                  <path d="M18 12a2 2 0 0 0-2 2c0 1.1.9 2 2 2h4v-4h-4z"></path>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
