{"version": 3, "file": "SVGRenderer.js", "sources": ["../../src/renderers/SVGRenderer.js"], "sourcesContent": ["import { Box2, <PERSON>, Color, Matrix3, Matrix4, Object3D, Vector3 } from 'three'\nimport { Projector, RenderableFace, RenderableLine, RenderableSprite } from '../renderers/Projector'\n\nclass SVGObject extends Object3D {\n  constructor(node) {\n    super()\n\n    this.isSVGObject = true\n\n    this.node = node\n  }\n}\n\nclass SVGRenderer {\n  constructor() {\n    let _renderData,\n      _elements,\n      _lights,\n      _svgWidth,\n      _svgHeight,\n      _svgWidthHalf,\n      _svgHeightHalf,\n      _v1,\n      _v2,\n      _v3,\n      _svgNode,\n      _pathCount = 0,\n      _precision = null,\n      _quality = 1,\n      _currentPath,\n      _currentStyle\n\n    const _this = this,\n      _clipBox = new Box2(),\n      _elemBox = new Box2(),\n      _color = new Color(),\n      _diffuseColor = new Color(),\n      _ambientLight = new Color(),\n      _directionalLights = new Color(),\n      _pointLights = new Color(),\n      _clearColor = new Color(),\n      _vector3 = new Vector3(), // Needed for PointLight\n      _centroid = new Vector3(),\n      _normal = new Vector3(),\n      _normalViewMatrix = new Matrix3(),\n      _viewMatrix = new Matrix4(),\n      _viewProjectionMatrix = new Matrix4(),\n      _svgPathPool = [],\n      _projector = new Projector(),\n      _svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')\n\n    this.domElement = _svg\n\n    this.autoClear = true\n    this.sortObjects = true\n    this.sortElements = true\n\n    this.overdraw = 0.5\n\n    this.info = {\n      render: {\n        vertices: 0,\n        faces: 0,\n      },\n    }\n\n    this.setQuality = function (quality) {\n      switch (quality) {\n        case 'high':\n          _quality = 1\n          break\n        case 'low':\n          _quality = 0\n          break\n      }\n    }\n\n    this.setClearColor = function (color) {\n      _clearColor.set(color)\n    }\n\n    this.setPixelRatio = function () {}\n\n    this.setSize = function (width, height) {\n      _svgWidth = width\n      _svgHeight = height\n      _svgWidthHalf = _svgWidth / 2\n      _svgHeightHalf = _svgHeight / 2\n\n      _svg.setAttribute('viewBox', -_svgWidthHalf + ' ' + -_svgHeightHalf + ' ' + _svgWidth + ' ' + _svgHeight)\n      _svg.setAttribute('width', _svgWidth)\n      _svg.setAttribute('height', _svgHeight)\n\n      _clipBox.min.set(-_svgWidthHalf, -_svgHeightHalf)\n      _clipBox.max.set(_svgWidthHalf, _svgHeightHalf)\n    }\n\n    this.getSize = function () {\n      return {\n        width: _svgWidth,\n        height: _svgHeight,\n      }\n    }\n\n    this.setPrecision = function (precision) {\n      _precision = precision\n    }\n\n    function removeChildNodes() {\n      _pathCount = 0\n\n      while (_svg.childNodes.length > 0) {\n        _svg.removeChild(_svg.childNodes[0])\n      }\n    }\n\n    function convert(c) {\n      return _precision !== null ? c.toFixed(_precision) : c\n    }\n\n    this.clear = function () {\n      removeChildNodes()\n      _svg.style.backgroundColor = _clearColor.getStyle()\n    }\n\n    this.render = function (scene, camera) {\n      if (camera instanceof Camera === false) {\n        console.error('THREE.SVGRenderer.render: camera is not an instance of Camera.')\n        return\n      }\n\n      const background = scene.background\n\n      if (background && background.isColor) {\n        removeChildNodes()\n        _svg.style.backgroundColor = background.getStyle()\n      } else if (this.autoClear === true) {\n        this.clear()\n      }\n\n      _this.info.render.vertices = 0\n      _this.info.render.faces = 0\n\n      _viewMatrix.copy(camera.matrixWorldInverse)\n      _viewProjectionMatrix.multiplyMatrices(camera.projectionMatrix, _viewMatrix)\n\n      _renderData = _projector.projectScene(scene, camera, this.sortObjects, this.sortElements)\n      _elements = _renderData.elements\n      _lights = _renderData.lights\n\n      _normalViewMatrix.getNormalMatrix(camera.matrixWorldInverse)\n\n      calculateLights(_lights)\n\n      // reset accumulated path\n\n      _currentPath = ''\n      _currentStyle = ''\n\n      for (let e = 0, el = _elements.length; e < el; e++) {\n        const element = _elements[e]\n        const material = element.material\n\n        if (material === undefined || material.opacity === 0) continue\n\n        _elemBox.makeEmpty()\n\n        if (element instanceof RenderableSprite) {\n          _v1 = element\n          _v1.x *= _svgWidthHalf\n          _v1.y *= -_svgHeightHalf\n\n          renderSprite(_v1, element, material)\n        } else if (element instanceof RenderableLine) {\n          _v1 = element.v1\n          _v2 = element.v2\n\n          _v1.positionScreen.x *= _svgWidthHalf\n          _v1.positionScreen.y *= -_svgHeightHalf\n          _v2.positionScreen.x *= _svgWidthHalf\n          _v2.positionScreen.y *= -_svgHeightHalf\n\n          _elemBox.setFromPoints([_v1.positionScreen, _v2.positionScreen])\n\n          if (_clipBox.intersectsBox(_elemBox) === true) {\n            renderLine(_v1, _v2, material)\n          }\n        } else if (element instanceof RenderableFace) {\n          _v1 = element.v1\n          _v2 = element.v2\n          _v3 = element.v3\n\n          if (_v1.positionScreen.z < -1 || _v1.positionScreen.z > 1) continue\n          if (_v2.positionScreen.z < -1 || _v2.positionScreen.z > 1) continue\n          if (_v3.positionScreen.z < -1 || _v3.positionScreen.z > 1) continue\n\n          _v1.positionScreen.x *= _svgWidthHalf\n          _v1.positionScreen.y *= -_svgHeightHalf\n          _v2.positionScreen.x *= _svgWidthHalf\n          _v2.positionScreen.y *= -_svgHeightHalf\n          _v3.positionScreen.x *= _svgWidthHalf\n          _v3.positionScreen.y *= -_svgHeightHalf\n\n          if (this.overdraw > 0) {\n            expand(_v1.positionScreen, _v2.positionScreen, this.overdraw)\n            expand(_v2.positionScreen, _v3.positionScreen, this.overdraw)\n            expand(_v3.positionScreen, _v1.positionScreen, this.overdraw)\n          }\n\n          _elemBox.setFromPoints([_v1.positionScreen, _v2.positionScreen, _v3.positionScreen])\n\n          if (_clipBox.intersectsBox(_elemBox) === true) {\n            renderFace3(_v1, _v2, _v3, element, material)\n          }\n        }\n      }\n\n      flushPath() // just to flush last svg:path\n\n      scene.traverseVisible(function (object) {\n        if (object.isSVGObject) {\n          _vector3.setFromMatrixPosition(object.matrixWorld)\n          _vector3.applyMatrix4(_viewProjectionMatrix)\n\n          if (_vector3.z < -1 || _vector3.z > 1) return\n\n          const x = _vector3.x * _svgWidthHalf\n          const y = -_vector3.y * _svgHeightHalf\n\n          const node = object.node\n          node.setAttribute('transform', 'translate(' + x + ',' + y + ')')\n\n          _svg.appendChild(node)\n        }\n      })\n    }\n\n    function calculateLights(lights) {\n      _ambientLight.setRGB(0, 0, 0)\n      _directionalLights.setRGB(0, 0, 0)\n      _pointLights.setRGB(0, 0, 0)\n\n      for (let l = 0, ll = lights.length; l < ll; l++) {\n        const light = lights[l]\n        const lightColor = light.color\n\n        if (light.isAmbientLight) {\n          _ambientLight.r += lightColor.r\n          _ambientLight.g += lightColor.g\n          _ambientLight.b += lightColor.b\n        } else if (light.isDirectionalLight) {\n          _directionalLights.r += lightColor.r\n          _directionalLights.g += lightColor.g\n          _directionalLights.b += lightColor.b\n        } else if (light.isPointLight) {\n          _pointLights.r += lightColor.r\n          _pointLights.g += lightColor.g\n          _pointLights.b += lightColor.b\n        }\n      }\n    }\n\n    function calculateLight(lights, position, normal, color) {\n      for (let l = 0, ll = lights.length; l < ll; l++) {\n        const light = lights[l]\n        const lightColor = light.color\n\n        if (light.isDirectionalLight) {\n          const lightPosition = _vector3.setFromMatrixPosition(light.matrixWorld).normalize()\n\n          let amount = normal.dot(lightPosition)\n\n          if (amount <= 0) continue\n\n          amount *= light.intensity\n\n          color.r += lightColor.r * amount\n          color.g += lightColor.g * amount\n          color.b += lightColor.b * amount\n        } else if (light.isPointLight) {\n          const lightPosition = _vector3.setFromMatrixPosition(light.matrixWorld)\n\n          let amount = normal.dot(_vector3.subVectors(lightPosition, position).normalize())\n\n          if (amount <= 0) continue\n\n          amount *= light.distance == 0 ? 1 : 1 - Math.min(position.distanceTo(lightPosition) / light.distance, 1)\n\n          if (amount == 0) continue\n\n          amount *= light.intensity\n\n          color.r += lightColor.r * amount\n          color.g += lightColor.g * amount\n          color.b += lightColor.b * amount\n        }\n      }\n    }\n\n    function renderSprite(v1, element, material) {\n      let scaleX = element.scale.x * _svgWidthHalf\n      let scaleY = element.scale.y * _svgHeightHalf\n\n      if (material.isPointsMaterial) {\n        scaleX *= material.size\n        scaleY *= material.size\n      }\n\n      const path =\n        'M' +\n        convert(v1.x - scaleX * 0.5) +\n        ',' +\n        convert(v1.y - scaleY * 0.5) +\n        'h' +\n        convert(scaleX) +\n        'v' +\n        convert(scaleY) +\n        'h' +\n        convert(-scaleX) +\n        'z'\n      let style = ''\n\n      if (material.isSpriteMaterial || material.isPointsMaterial) {\n        style = 'fill:' + material.color.getStyle() + ';fill-opacity:' + material.opacity\n      }\n\n      addPath(style, path)\n    }\n\n    function renderLine(v1, v2, material) {\n      const path =\n        'M' +\n        convert(v1.positionScreen.x) +\n        ',' +\n        convert(v1.positionScreen.y) +\n        'L' +\n        convert(v2.positionScreen.x) +\n        ',' +\n        convert(v2.positionScreen.y)\n\n      if (material.isLineBasicMaterial) {\n        let style =\n          'fill:none;stroke:' +\n          material.color.getStyle() +\n          ';stroke-opacity:' +\n          material.opacity +\n          ';stroke-width:' +\n          material.linewidth +\n          ';stroke-linecap:' +\n          material.linecap\n\n        if (material.isLineDashedMaterial) {\n          style = style + ';stroke-dasharray:' + material.dashSize + ',' + material.gapSize\n        }\n\n        addPath(style, path)\n      }\n    }\n\n    function renderFace3(v1, v2, v3, element, material) {\n      _this.info.render.vertices += 3\n      _this.info.render.faces++\n\n      const path =\n        'M' +\n        convert(v1.positionScreen.x) +\n        ',' +\n        convert(v1.positionScreen.y) +\n        'L' +\n        convert(v2.positionScreen.x) +\n        ',' +\n        convert(v2.positionScreen.y) +\n        'L' +\n        convert(v3.positionScreen.x) +\n        ',' +\n        convert(v3.positionScreen.y) +\n        'z'\n      let style = ''\n\n      if (material.isMeshBasicMaterial) {\n        _color.copy(material.color)\n\n        if (material.vertexColors) {\n          _color.multiply(element.color)\n        }\n      } else if (material.isMeshLambertMaterial || material.isMeshPhongMaterial || material.isMeshStandardMaterial) {\n        _diffuseColor.copy(material.color)\n\n        if (material.vertexColors) {\n          _diffuseColor.multiply(element.color)\n        }\n\n        _color.copy(_ambientLight)\n\n        _centroid.copy(v1.positionWorld).add(v2.positionWorld).add(v3.positionWorld).divideScalar(3)\n\n        calculateLight(_lights, _centroid, element.normalModel, _color)\n\n        _color.multiply(_diffuseColor).add(material.emissive)\n      } else if (material.isMeshNormalMaterial) {\n        _normal.copy(element.normalModel).applyMatrix3(_normalViewMatrix).normalize()\n\n        _color.setRGB(_normal.x, _normal.y, _normal.z).multiplyScalar(0.5).addScalar(0.5)\n      }\n\n      if (material.wireframe) {\n        style =\n          'fill:none;stroke:' +\n          _color.getStyle() +\n          ';stroke-opacity:' +\n          material.opacity +\n          ';stroke-width:' +\n          material.wireframeLinewidth +\n          ';stroke-linecap:' +\n          material.wireframeLinecap +\n          ';stroke-linejoin:' +\n          material.wireframeLinejoin\n      } else {\n        style = 'fill:' + _color.getStyle() + ';fill-opacity:' + material.opacity\n      }\n\n      addPath(style, path)\n    }\n\n    // Hide anti-alias gaps\n\n    function expand(v1, v2, pixels) {\n      let x = v2.x - v1.x,\n        y = v2.y - v1.y\n      const det = x * x + y * y\n\n      if (det === 0) return\n\n      const idet = pixels / Math.sqrt(det)\n\n      x *= idet\n      y *= idet\n\n      v2.x += x\n      v2.y += y\n      v1.x -= x\n      v1.y -= y\n    }\n\n    function addPath(style, path) {\n      if (_currentStyle === style) {\n        _currentPath += path\n      } else {\n        flushPath()\n\n        _currentStyle = style\n        _currentPath = path\n      }\n    }\n\n    function flushPath() {\n      if (_currentPath) {\n        _svgNode = getPathNode(_pathCount++)\n        _svgNode.setAttribute('d', _currentPath)\n        _svgNode.setAttribute('style', _currentStyle)\n        _svg.appendChild(_svgNode)\n      }\n\n      _currentPath = ''\n      _currentStyle = ''\n    }\n\n    function getPathNode(id) {\n      if (_svgPathPool[id] == null) {\n        _svgPathPool[id] = document.createElementNS('http://www.w3.org/2000/svg', 'path')\n\n        if (_quality == 0) {\n          _svgPathPool[id].setAttribute('shape-rendering', 'crispEdges') //optimizeSpeed\n        }\n\n        return _svgPathPool[id]\n      }\n\n      return _svgPathPool[id]\n    }\n  }\n}\n\nexport { SVGObject, SVGRenderer }\n"], "names": [], "mappings": ";;AAGA,MAAM,kBAAkB,SAAS;AAAA,EAC/B,YAAY,MAAM;AAChB,UAAO;AAEP,SAAK,cAAc;AAEnB,SAAK,OAAO;AAAA,EACb;AACH;AAEA,MAAM,YAAY;AAAA,EAChB,cAAc;AACZ,QAAI,aACF,WACA,SACA,WACA,YACA,eACA,gBACA,KACA,KACA,KACA,UACA,aAAa,GACb,aAAa,MACb,WAAW,GACX,cACA;AAEF,UAAM,QAAQ,MACZ,WAAW,IAAI,KAAM,GACrB,WAAW,IAAI,KAAM,GACrB,SAAS,IAAI,MAAO,GACpB,gBAAgB,IAAI,MAAO,GAC3B,gBAAgB,IAAI,MAAO,GAC3B,qBAAqB,IAAI,MAAO,GAChC,eAAe,IAAI,MAAO,GAC1B,cAAc,IAAI,MAAO,GACzB,WAAW,IAAI,QAAS,GACxB,YAAY,IAAI,QAAS,GACzB,UAAU,IAAI,QAAS,GACvB,oBAAoB,IAAI,QAAS,GACjC,cAAc,IAAI,QAAS,GAC3B,wBAAwB,IAAI,QAAS,GACrC,eAAe,CAAE,GACjB,aAAa,IAAI,UAAW,GAC5B,OAAO,SAAS,gBAAgB,8BAA8B,KAAK;AAErE,SAAK,aAAa;AAElB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,eAAe;AAEpB,SAAK,WAAW;AAEhB,SAAK,OAAO;AAAA,MACV,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACR;AAAA,IACF;AAED,SAAK,aAAa,SAAU,SAAS;AACnC,cAAQ,SAAO;AAAA,QACb,KAAK;AACH,qBAAW;AACX;AAAA,QACF,KAAK;AACH,qBAAW;AACX;AAAA,MACH;AAAA,IACF;AAED,SAAK,gBAAgB,SAAU,OAAO;AACpC,kBAAY,IAAI,KAAK;AAAA,IACtB;AAED,SAAK,gBAAgB,WAAY;AAAA,IAAE;AAEnC,SAAK,UAAU,SAAU,OAAO,QAAQ;AACtC,kBAAY;AACZ,mBAAa;AACb,sBAAgB,YAAY;AAC5B,uBAAiB,aAAa;AAE9B,WAAK,aAAa,WAAW,CAAC,gBAAgB,MAAM,CAAC,iBAAiB,MAAM,YAAY,MAAM,UAAU;AACxG,WAAK,aAAa,SAAS,SAAS;AACpC,WAAK,aAAa,UAAU,UAAU;AAEtC,eAAS,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc;AAChD,eAAS,IAAI,IAAI,eAAe,cAAc;AAAA,IAC/C;AAED,SAAK,UAAU,WAAY;AACzB,aAAO;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACF;AAED,SAAK,eAAe,SAAU,WAAW;AACvC,mBAAa;AAAA,IACd;AAED,aAAS,mBAAmB;AAC1B,mBAAa;AAEb,aAAO,KAAK,WAAW,SAAS,GAAG;AACjC,aAAK,YAAY,KAAK,WAAW,CAAC,CAAC;AAAA,MACpC;AAAA,IACF;AAED,aAAS,QAAQ,GAAG;AAClB,aAAO,eAAe,OAAO,EAAE,QAAQ,UAAU,IAAI;AAAA,IACtD;AAED,SAAK,QAAQ,WAAY;AACvB,uBAAkB;AAClB,WAAK,MAAM,kBAAkB,YAAY,SAAU;AAAA,IACpD;AAED,SAAK,SAAS,SAAU,OAAO,QAAQ;AACrC,UAAI,kBAAkB,WAAW,OAAO;AACtC,gBAAQ,MAAM,gEAAgE;AAC9E;AAAA,MACD;AAED,YAAM,aAAa,MAAM;AAEzB,UAAI,cAAc,WAAW,SAAS;AACpC,yBAAkB;AAClB,aAAK,MAAM,kBAAkB,WAAW,SAAU;AAAA,MAC1D,WAAiB,KAAK,cAAc,MAAM;AAClC,aAAK,MAAO;AAAA,MACb;AAED,YAAM,KAAK,OAAO,WAAW;AAC7B,YAAM,KAAK,OAAO,QAAQ;AAE1B,kBAAY,KAAK,OAAO,kBAAkB;AAC1C,4BAAsB,iBAAiB,OAAO,kBAAkB,WAAW;AAE3E,oBAAc,WAAW,aAAa,OAAO,QAAQ,KAAK,aAAa,KAAK,YAAY;AACxF,kBAAY,YAAY;AACxB,gBAAU,YAAY;AAEtB,wBAAkB,gBAAgB,OAAO,kBAAkB;AAE3D,sBAAgB,OAAO;AAIvB,qBAAe;AACf,sBAAgB;AAEhB,eAAS,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,KAAK;AAClD,cAAM,UAAU,UAAU,CAAC;AAC3B,cAAM,WAAW,QAAQ;AAEzB,YAAI,aAAa,UAAa,SAAS,YAAY;AAAG;AAEtD,iBAAS,UAAW;AAEpB,YAAI,mBAAmB,kBAAkB;AACvC,gBAAM;AACN,cAAI,KAAK;AACT,cAAI,KAAK,CAAC;AAEV,uBAAa,KAAK,SAAS,QAAQ;AAAA,QAC7C,WAAmB,mBAAmB,gBAAgB;AAC5C,gBAAM,QAAQ;AACd,gBAAM,QAAQ;AAEd,cAAI,eAAe,KAAK;AACxB,cAAI,eAAe,KAAK,CAAC;AACzB,cAAI,eAAe,KAAK;AACxB,cAAI,eAAe,KAAK,CAAC;AAEzB,mBAAS,cAAc,CAAC,IAAI,gBAAgB,IAAI,cAAc,CAAC;AAE/D,cAAI,SAAS,cAAc,QAAQ,MAAM,MAAM;AAC7C,uBAAW,KAAK,KAAK,QAAQ;AAAA,UAC9B;AAAA,QACX,WAAmB,mBAAmB,gBAAgB;AAC5C,gBAAM,QAAQ;AACd,gBAAM,QAAQ;AACd,gBAAM,QAAQ;AAEd,cAAI,IAAI,eAAe,IAAI,MAAM,IAAI,eAAe,IAAI;AAAG;AAC3D,cAAI,IAAI,eAAe,IAAI,MAAM,IAAI,eAAe,IAAI;AAAG;AAC3D,cAAI,IAAI,eAAe,IAAI,MAAM,IAAI,eAAe,IAAI;AAAG;AAE3D,cAAI,eAAe,KAAK;AACxB,cAAI,eAAe,KAAK,CAAC;AACzB,cAAI,eAAe,KAAK;AACxB,cAAI,eAAe,KAAK,CAAC;AACzB,cAAI,eAAe,KAAK;AACxB,cAAI,eAAe,KAAK,CAAC;AAEzB,cAAI,KAAK,WAAW,GAAG;AACrB,mBAAO,IAAI,gBAAgB,IAAI,gBAAgB,KAAK,QAAQ;AAC5D,mBAAO,IAAI,gBAAgB,IAAI,gBAAgB,KAAK,QAAQ;AAC5D,mBAAO,IAAI,gBAAgB,IAAI,gBAAgB,KAAK,QAAQ;AAAA,UAC7D;AAED,mBAAS,cAAc,CAAC,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,cAAc,CAAC;AAEnF,cAAI,SAAS,cAAc,QAAQ,MAAM,MAAM;AAC7C,wBAAY,KAAK,KAAK,KAAK,SAAS,QAAQ;AAAA,UAC7C;AAAA,QACF;AAAA,MACF;AAED,gBAAW;AAEX,YAAM,gBAAgB,SAAU,QAAQ;AACtC,YAAI,OAAO,aAAa;AACtB,mBAAS,sBAAsB,OAAO,WAAW;AACjD,mBAAS,aAAa,qBAAqB;AAE3C,cAAI,SAAS,IAAI,MAAM,SAAS,IAAI;AAAG;AAEvC,gBAAM,IAAI,SAAS,IAAI;AACvB,gBAAM,IAAI,CAAC,SAAS,IAAI;AAExB,gBAAM,OAAO,OAAO;AACpB,eAAK,aAAa,aAAa,eAAe,IAAI,MAAM,IAAI,GAAG;AAE/D,eAAK,YAAY,IAAI;AAAA,QACtB;AAAA,MACT,CAAO;AAAA,IACF;AAED,aAAS,gBAAgB,QAAQ;AAC/B,oBAAc,OAAO,GAAG,GAAG,CAAC;AAC5B,yBAAmB,OAAO,GAAG,GAAG,CAAC;AACjC,mBAAa,OAAO,GAAG,GAAG,CAAC;AAE3B,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,aAAa,MAAM;AAEzB,YAAI,MAAM,gBAAgB;AACxB,wBAAc,KAAK,WAAW;AAC9B,wBAAc,KAAK,WAAW;AAC9B,wBAAc,KAAK,WAAW;AAAA,QACxC,WAAmB,MAAM,oBAAoB;AACnC,6BAAmB,KAAK,WAAW;AACnC,6BAAmB,KAAK,WAAW;AACnC,6BAAmB,KAAK,WAAW;AAAA,QAC7C,WAAmB,MAAM,cAAc;AAC7B,uBAAa,KAAK,WAAW;AAC7B,uBAAa,KAAK,WAAW;AAC7B,uBAAa,KAAK,WAAW;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAED,aAAS,eAAe,QAAQ,UAAU,QAAQ,OAAO;AACvD,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,aAAa,MAAM;AAEzB,YAAI,MAAM,oBAAoB;AAC5B,gBAAM,gBAAgB,SAAS,sBAAsB,MAAM,WAAW,EAAE,UAAW;AAEnF,cAAI,SAAS,OAAO,IAAI,aAAa;AAErC,cAAI,UAAU;AAAG;AAEjB,oBAAU,MAAM;AAEhB,gBAAM,KAAK,WAAW,IAAI;AAC1B,gBAAM,KAAK,WAAW,IAAI;AAC1B,gBAAM,KAAK,WAAW,IAAI;AAAA,QACpC,WAAmB,MAAM,cAAc;AAC7B,gBAAM,gBAAgB,SAAS,sBAAsB,MAAM,WAAW;AAEtE,cAAI,SAAS,OAAO,IAAI,SAAS,WAAW,eAAe,QAAQ,EAAE,WAAW;AAEhF,cAAI,UAAU;AAAG;AAEjB,oBAAU,MAAM,YAAY,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,WAAW,aAAa,IAAI,MAAM,UAAU,CAAC;AAEvG,cAAI,UAAU;AAAG;AAEjB,oBAAU,MAAM;AAEhB,gBAAM,KAAK,WAAW,IAAI;AAC1B,gBAAM,KAAK,WAAW,IAAI;AAC1B,gBAAM,KAAK,WAAW,IAAI;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAED,aAAS,aAAa,IAAI,SAAS,UAAU;AAC3C,UAAI,SAAS,QAAQ,MAAM,IAAI;AAC/B,UAAI,SAAS,QAAQ,MAAM,IAAI;AAE/B,UAAI,SAAS,kBAAkB;AAC7B,kBAAU,SAAS;AACnB,kBAAU,SAAS;AAAA,MACpB;AAED,YAAM,OACJ,MACA,QAAQ,GAAG,IAAI,SAAS,GAAG,IAC3B,MACA,QAAQ,GAAG,IAAI,SAAS,GAAG,IAC3B,MACA,QAAQ,MAAM,IACd,MACA,QAAQ,MAAM,IACd,MACA,QAAQ,CAAC,MAAM,IACf;AACF,UAAI,QAAQ;AAEZ,UAAI,SAAS,oBAAoB,SAAS,kBAAkB;AAC1D,gBAAQ,UAAU,SAAS,MAAM,aAAa,mBAAmB,SAAS;AAAA,MAC3E;AAED,cAAQ,OAAO,IAAI;AAAA,IACpB;AAED,aAAS,WAAW,IAAI,IAAI,UAAU;AACpC,YAAM,OACJ,MACA,QAAQ,GAAG,eAAe,CAAC,IAC3B,MACA,QAAQ,GAAG,eAAe,CAAC,IAC3B,MACA,QAAQ,GAAG,eAAe,CAAC,IAC3B,MACA,QAAQ,GAAG,eAAe,CAAC;AAE7B,UAAI,SAAS,qBAAqB;AAChC,YAAI,QACF,sBACA,SAAS,MAAM,SAAU,IACzB,qBACA,SAAS,UACT,mBACA,SAAS,YACT,qBACA,SAAS;AAEX,YAAI,SAAS,sBAAsB;AACjC,kBAAQ,QAAQ,uBAAuB,SAAS,WAAW,MAAM,SAAS;AAAA,QAC3E;AAED,gBAAQ,OAAO,IAAI;AAAA,MACpB;AAAA,IACF;AAED,aAAS,YAAY,IAAI,IAAI,IAAI,SAAS,UAAU;AAClD,YAAM,KAAK,OAAO,YAAY;AAC9B,YAAM,KAAK,OAAO;AAElB,YAAM,OACJ,MACA,QAAQ,GAAG,eAAe,CAAC,IAC3B,MACA,QAAQ,GAAG,eAAe,CAAC,IAC3B,MACA,QAAQ,GAAG,eAAe,CAAC,IAC3B,MACA,QAAQ,GAAG,eAAe,CAAC,IAC3B,MACA,QAAQ,GAAG,eAAe,CAAC,IAC3B,MACA,QAAQ,GAAG,eAAe,CAAC,IAC3B;AACF,UAAI,QAAQ;AAEZ,UAAI,SAAS,qBAAqB;AAChC,eAAO,KAAK,SAAS,KAAK;AAE1B,YAAI,SAAS,cAAc;AACzB,iBAAO,SAAS,QAAQ,KAAK;AAAA,QAC9B;AAAA,MACT,WAAiB,SAAS,yBAAyB,SAAS,uBAAuB,SAAS,wBAAwB;AAC5G,sBAAc,KAAK,SAAS,KAAK;AAEjC,YAAI,SAAS,cAAc;AACzB,wBAAc,SAAS,QAAQ,KAAK;AAAA,QACrC;AAED,eAAO,KAAK,aAAa;AAEzB,kBAAU,KAAK,GAAG,aAAa,EAAE,IAAI,GAAG,aAAa,EAAE,IAAI,GAAG,aAAa,EAAE,aAAa,CAAC;AAE3F,uBAAe,SAAS,WAAW,QAAQ,aAAa,MAAM;AAE9D,eAAO,SAAS,aAAa,EAAE,IAAI,SAAS,QAAQ;AAAA,MAC5D,WAAiB,SAAS,sBAAsB;AACxC,gBAAQ,KAAK,QAAQ,WAAW,EAAE,aAAa,iBAAiB,EAAE,UAAW;AAE7E,eAAO,OAAO,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,EAAE,eAAe,GAAG,EAAE,UAAU,GAAG;AAAA,MACjF;AAED,UAAI,SAAS,WAAW;AACtB,gBACE,sBACA,OAAO,SAAU,IACjB,qBACA,SAAS,UACT,mBACA,SAAS,qBACT,qBACA,SAAS,mBACT,sBACA,SAAS;AAAA,MACnB,OAAa;AACL,gBAAQ,UAAU,OAAO,SAAQ,IAAK,mBAAmB,SAAS;AAAA,MACnE;AAED,cAAQ,OAAO,IAAI;AAAA,IACpB;AAID,aAAS,OAAO,IAAI,IAAI,QAAQ;AAC9B,UAAI,IAAI,GAAG,IAAI,GAAG,GAChB,IAAI,GAAG,IAAI,GAAG;AAChB,YAAM,MAAM,IAAI,IAAI,IAAI;AAExB,UAAI,QAAQ;AAAG;AAEf,YAAM,OAAO,SAAS,KAAK,KAAK,GAAG;AAEnC,WAAK;AACL,WAAK;AAEL,SAAG,KAAK;AACR,SAAG,KAAK;AACR,SAAG,KAAK;AACR,SAAG,KAAK;AAAA,IACT;AAED,aAAS,QAAQ,OAAO,MAAM;AAC5B,UAAI,kBAAkB,OAAO;AAC3B,wBAAgB;AAAA,MACxB,OAAa;AACL,kBAAW;AAEX,wBAAgB;AAChB,uBAAe;AAAA,MAChB;AAAA,IACF;AAED,aAAS,YAAY;AACnB,UAAI,cAAc;AAChB,mBAAW,YAAY,YAAY;AACnC,iBAAS,aAAa,KAAK,YAAY;AACvC,iBAAS,aAAa,SAAS,aAAa;AAC5C,aAAK,YAAY,QAAQ;AAAA,MAC1B;AAED,qBAAe;AACf,sBAAgB;AAAA,IACjB;AAED,aAAS,YAAY,IAAI;AACvB,UAAI,aAAa,EAAE,KAAK,MAAM;AAC5B,qBAAa,EAAE,IAAI,SAAS,gBAAgB,8BAA8B,MAAM;AAEhF,YAAI,YAAY,GAAG;AACjB,uBAAa,EAAE,EAAE,aAAa,mBAAmB,YAAY;AAAA,QAC9D;AAED,eAAO,aAAa,EAAE;AAAA,MACvB;AAED,aAAO,aAAa,EAAE;AAAA,IACvB;AAAA,EACF;AACH;"}