"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_3d_Interactive3DModel_tsx",{

/***/ "(app-pages-browser)/./src/components/3d/Interactive3DModel.tsx":
/*!**************************************************!*\
  !*** ./src/components/3d/Interactive3DModel.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Interactive3DModel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/@react-three/drei/core/Gltf.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/@react-three/drei/core/Environment.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Model() {\n    _s();\n    const { scene } = (0,_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF)('/scene.gltf');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n        object: scene,\n        scale: [\n            0.4,\n            0.4,\n            0.4\n        ],\n        position: [\n            0,\n            0,\n            0\n        ],\n        rotation: [\n            0,\n            0,\n            0\n        ]\n    }, void 0, false, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_s(Model, \"o+hqw2nGnmzAsiWsKcbG4W2mWg4=\", false, function() {\n    return [\n        _react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF\n    ];\n});\n_c = Model;\nfunction LoadingFallback() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                args: [\n                    1,\n                    1,\n                    1\n                ]\n            }, void 0, false, {\n                fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                color: \"#a3e635\"\n            }, void 0, false, {\n                fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LoadingFallback;\nfunction Interactive3DModel() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.Canvas, {\n            camera: {\n                position: [\n                    0,\n                    0,\n                    12\n                ],\n                fov: 35\n            },\n            style: {\n                background: 'transparent'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ambientLight\", {\n                    intensity: 0.5\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"directionalLight\", {\n                    position: [\n                        10,\n                        10,\n                        5\n                    ],\n                    intensity: 1\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pointLight\", {\n                    position: [\n                        -10,\n                        -10,\n                        -5\n                    ],\n                    intensity: 0.5\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingFallback, {}, void 0, false, {\n                        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 29\n                    }, void 0),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Model, {}, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.Environment, {\n                            preset: \"studio\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_5__.OrbitControls, {\n                    enablePan: true,\n                    enableZoom: true,\n                    enableRotate: true,\n                    autoRotate: true,\n                    autoRotateSpeed: 1,\n                    minDistance: 5,\n                    maxDistance: 20,\n                    target: [\n                        0,\n                        0,\n                        0\n                    ]\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c2 = Interactive3DModel;\n// Preload the model\n_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF.preload('/scene.gltf');\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Model\");\n$RefreshReg$(_c1, \"LoadingFallback\");\n$RefreshReg$(_c2, \"Interactive3DModel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/3d/Interactive3DModel.tsx\n"));

/***/ })

});