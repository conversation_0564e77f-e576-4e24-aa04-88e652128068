"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_3d_Interactive3DModel_tsx",{

/***/ "(app-pages-browser)/./src/components/3d/Interactive3DModel.tsx":
/*!**************************************************!*\
  !*** ./src/components/3d/Interactive3DModel.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Interactive3DModel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/@react-three/drei/core/Gltf.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/@react-three/drei/core/Environment.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Model() {\n    _s();\n    const { scene } = (0,_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF)('/scene.gltf');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n        object: scene,\n        scale: [\n            0.6,\n            0.6,\n            0.6\n        ],\n        position: [\n            0,\n            -0.5,\n            0\n        ],\n        rotation: [\n            0,\n            0,\n            0\n        ]\n    }, void 0, false, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_s(Model, \"o+hqw2nGnmzAsiWsKcbG4W2mWg4=\", false, function() {\n    return [\n        _react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF\n    ];\n});\n_c = Model;\nfunction LoadingFallback() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"boxGeometry\", {\n                args: [\n                    1,\n                    1,\n                    1\n                ]\n            }, void 0, false, {\n                fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                color: \"#a3e635\"\n            }, void 0, false, {\n                fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LoadingFallback;\nfunction Interactive3DModel() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.Canvas, {\n            camera: {\n                position: [\n                    0,\n                    0,\n                    10\n                ],\n                fov: 40\n            },\n            style: {\n                background: 'transparent'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ambientLight\", {\n                    intensity: 0.5\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"directionalLight\", {\n                    position: [\n                        10,\n                        10,\n                        5\n                    ],\n                    intensity: 1\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pointLight\", {\n                    position: [\n                        -10,\n                        -10,\n                        -5\n                    ],\n                    intensity: 0.5\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingFallback, {}, void 0, false, {\n                        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 29\n                    }, void 0),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Model, {}, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.Environment, {\n                            preset: \"studio\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_5__.OrbitControls, {\n                    enablePan: true,\n                    enableZoom: true,\n                    enableRotate: true,\n                    autoRotate: true,\n                    autoRotateSpeed: 1,\n                    minDistance: 5,\n                    maxDistance: 20,\n                    target: [\n                        0,\n                        0,\n                        0\n                    ]\n                }, void 0, false, {\n                    fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\courtone\\\\courtone\\\\src\\\\components\\\\3d\\\\Interactive3DModel.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c2 = Interactive3DModel;\n// Preload the model\n_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF.preload('/scene.gltf');\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Model\");\n$RefreshReg$(_c1, \"LoadingFallback\");\n$RefreshReg$(_c2, \"Interactive3DModel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/3d/Interactive3DModel.tsx\n"));

/***/ })

});