{"version": 3, "file": "TAARenderPass.cjs", "sources": ["../../src/postprocessing/TAARenderPass.js"], "sourcesContent": ["import { HalfFloatType, WebGLRenderTarget } from 'three'\nimport { SSAARenderPass } from './SSAARenderPass'\n\n/**\n *\n * Temporal Anti-Aliasing Render Pass\n *\n * When there is no motion in the scene, the TAA render pass accumulates jittered camera samples across frames to create a high quality anti-aliased result.\n *\n * References:\n *\n * TODO: Add support for motion vector pas so that accumulation of samples across frames can occur on dynamics scenes.\n *\n */\n\nclass TAARenderPass extends SSAARenderPass {\n  constructor(scene, camera, clearColor, clearAlpha) {\n    super(scene, camera, clearColor, clearAlpha)\n\n    this.sampleLevel = 0\n    this.accumulate = false\n    this.accumulateIndex = -1\n  }\n\n  render(renderer, writeBuffer, readBuffer, deltaTime) {\n    if (this.accumulate === false) {\n      super.render(renderer, writeBuffer, readBuffer, deltaTime)\n\n      this.accumulateIndex = -1\n      return\n    }\n\n    const jitterOffsets = _JitterVectors[5]\n\n    if (this.sampleRenderTarget === undefined) {\n      this.sampleRenderTarget = new WebGLRenderTarget(readBuffer.width, readBuffer.height, { type: HalfFloatType })\n      this.sampleRenderTarget.texture.name = 'TAARenderPass.sample'\n    }\n\n    if (this.holdRenderTarget === undefined) {\n      this.holdRenderTarget = new WebGLRenderTarget(readBuffer.width, readBuffer.height, { type: HalfFloatType })\n      this.holdRenderTarget.texture.name = 'TAARenderPass.hold'\n    }\n\n    if (this.accumulateIndex === -1) {\n      super.render(renderer, this.holdRenderTarget, readBuffer, deltaTime)\n\n      this.accumulateIndex = 0\n    }\n\n    const autoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    renderer.getClearColor(this._oldClearColor)\n    const oldClearAlpha = renderer.getClearAlpha()\n\n    const sampleWeight = 1.0 / jitterOffsets.length\n\n    if (this.accumulateIndex >= 0 && this.accumulateIndex < jitterOffsets.length) {\n      this.copyUniforms['opacity'].value = sampleWeight\n      this.copyUniforms['tDiffuse'].value = writeBuffer.texture\n\n      // render the scene multiple times, each slightly jitter offset from the last and accumulate the results.\n      const numSamplesPerFrame = Math.pow(2, this.sampleLevel)\n      for (let i = 0; i < numSamplesPerFrame; i++) {\n        const j = this.accumulateIndex\n        const jitterOffset = jitterOffsets[j]\n\n        if (this.camera.setViewOffset) {\n          this.camera.setViewOffset(\n            readBuffer.width,\n            readBuffer.height,\n            jitterOffset[0] * 0.0625,\n            jitterOffset[1] * 0.0625, // 0.0625 = 1 / 16\n            readBuffer.width,\n            readBuffer.height,\n          )\n        }\n\n        renderer.setRenderTarget(writeBuffer)\n        renderer.setClearColor(this.clearColor, this.clearAlpha)\n        renderer.clear()\n        renderer.render(this.scene, this.camera)\n\n        renderer.setRenderTarget(this.sampleRenderTarget)\n        if (this.accumulateIndex === 0) {\n          renderer.setClearColor(0x000000, 0.0)\n          renderer.clear()\n        }\n\n        this.fsQuad.render(renderer)\n\n        this.accumulateIndex++\n\n        if (this.accumulateIndex >= jitterOffsets.length) break\n      }\n\n      if (this.camera.clearViewOffset) this.camera.clearViewOffset()\n    }\n\n    renderer.setClearColor(this.clearColor, this.clearAlpha)\n    const accumulationWeight = this.accumulateIndex * sampleWeight\n\n    if (accumulationWeight > 0) {\n      this.copyUniforms['opacity'].value = 1.0\n      this.copyUniforms['tDiffuse'].value = this.sampleRenderTarget.texture\n      renderer.setRenderTarget(writeBuffer)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n\n    if (accumulationWeight < 1.0) {\n      this.copyUniforms['opacity'].value = 1.0 - accumulationWeight\n      this.copyUniforms['tDiffuse'].value = this.holdRenderTarget.texture\n      renderer.setRenderTarget(writeBuffer)\n      this.fsQuad.render(renderer)\n    }\n\n    renderer.autoClear = autoClear\n    renderer.setClearColor(this._oldClearColor, oldClearAlpha)\n  }\n\n  dispose() {\n    super.dispose()\n\n    if (this.sampleRenderTarget !== undefined) this.sampleRenderTarget.dispose()\n    if (this.holdRenderTarget !== undefined) this.holdRenderTarget.dispose()\n  }\n}\n\n// prettier-ignore\nconst _JitterVectors = [\n\t[\n\t\t[ 0, 0 ]\n\t],\n\t[\n\t\t[ 4, 4 ], [ - 4, - 4 ]\n\t],\n\t[\n\t\t[ - 2, - 6 ], [ 6, - 2 ], [ - 6, 2 ], [ 2, 6 ]\n\t],\n\t[\n\t\t[ 1, - 3 ], [ - 1, 3 ], [ 5, 1 ], [ - 3, - 5 ],\n\t\t[ - 5, 5 ], [ - 7, - 1 ], [ 3, 7 ], [ 7, - 7 ]\n\t],\n\t[\n\t\t[ 1, 1 ], [ - 1, - 3 ], [ - 3, 2 ], [ 4, - 1 ],\n\t\t[ - 5, - 2 ], [ 2, 5 ], [ 5, 3 ], [ 3, - 5 ],\n\t\t[ - 2, 6 ], [ 0, - 7 ], [ - 4, - 6 ], [ - 6, 4 ],\n\t\t[ - 8, 0 ], [ 7, - 4 ], [ 6, 7 ], [ - 7, - 8 ]\n\t],\n\t[\n\t\t[ - 4, - 7 ], [ - 7, - 5 ], [ - 3, - 5 ], [ - 5, - 4 ],\n\t\t[ - 1, - 4 ], [ - 2, - 2 ], [ - 6, - 1 ], [ - 4, 0 ],\n\t\t[ - 7, 1 ], [ - 1, 2 ], [ - 6, 3 ], [ - 3, 3 ],\n\t\t[ - 7, 6 ], [ - 3, 6 ], [ - 5, 7 ], [ - 1, 7 ],\n\t\t[ 5, - 7 ], [ 1, - 6 ], [ 6, - 5 ], [ 4, - 4 ],\n\t\t[ 2, - 3 ], [ 7, - 2 ], [ 1, - 1 ], [ 4, - 1 ],\n\t\t[ 2, 1 ], [ 6, 2 ], [ 0, 4 ], [ 4, 4 ],\n\t\t[ 2, 5 ], [ 7, 5 ], [ 5, 6 ], [ 3, 7 ]\n\t]\n];\n\nexport { TAARenderPass }\n"], "names": ["SSAARenderPass", "WebGLRenderTarget", "HalfFloatType"], "mappings": ";;;;AAeA,MAAM,sBAAsBA,eAAAA,eAAe;AAAA,EACzC,YAAY,OAAO,QAAQ,YAAY,YAAY;AACjD,UAAM,OAAO,QAAQ,YAAY,UAAU;AAE3C,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AAAA,EACxB;AAAA,EAED,OAAO,UAAU,aAAa,YAAY,WAAW;AACnD,QAAI,KAAK,eAAe,OAAO;AAC7B,YAAM,OAAO,UAAU,aAAa,YAAY,SAAS;AAEzD,WAAK,kBAAkB;AACvB;AAAA,IACD;AAED,UAAM,gBAAgB,eAAe,CAAC;AAEtC,QAAI,KAAK,uBAAuB,QAAW;AACzC,WAAK,qBAAqB,IAAIC,MAAiB,kBAAC,WAAW,OAAO,WAAW,QAAQ,EAAE,MAAMC,MAAAA,eAAe;AAC5G,WAAK,mBAAmB,QAAQ,OAAO;AAAA,IACxC;AAED,QAAI,KAAK,qBAAqB,QAAW;AACvC,WAAK,mBAAmB,IAAID,MAAiB,kBAAC,WAAW,OAAO,WAAW,QAAQ,EAAE,MAAMC,MAAAA,eAAe;AAC1G,WAAK,iBAAiB,QAAQ,OAAO;AAAA,IACtC;AAED,QAAI,KAAK,oBAAoB,IAAI;AAC/B,YAAM,OAAO,UAAU,KAAK,kBAAkB,YAAY,SAAS;AAEnE,WAAK,kBAAkB;AAAA,IACxB;AAED,UAAM,YAAY,SAAS;AAC3B,aAAS,YAAY;AAErB,aAAS,cAAc,KAAK,cAAc;AAC1C,UAAM,gBAAgB,SAAS,cAAe;AAE9C,UAAM,eAAe,IAAM,cAAc;AAEzC,QAAI,KAAK,mBAAmB,KAAK,KAAK,kBAAkB,cAAc,QAAQ;AAC5E,WAAK,aAAa,SAAS,EAAE,QAAQ;AACrC,WAAK,aAAa,UAAU,EAAE,QAAQ,YAAY;AAGlD,YAAM,qBAAqB,KAAK,IAAI,GAAG,KAAK,WAAW;AACvD,eAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK;AAC3C,cAAM,IAAI,KAAK;AACf,cAAM,eAAe,cAAc,CAAC;AAEpC,YAAI,KAAK,OAAO,eAAe;AAC7B,eAAK,OAAO;AAAA,YACV,WAAW;AAAA,YACX,WAAW;AAAA,YACX,aAAa,CAAC,IAAI;AAAA,YAClB,aAAa,CAAC,IAAI;AAAA;AAAA,YAClB,WAAW;AAAA,YACX,WAAW;AAAA,UACZ;AAAA,QACF;AAED,iBAAS,gBAAgB,WAAW;AACpC,iBAAS,cAAc,KAAK,YAAY,KAAK,UAAU;AACvD,iBAAS,MAAO;AAChB,iBAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AAEvC,iBAAS,gBAAgB,KAAK,kBAAkB;AAChD,YAAI,KAAK,oBAAoB,GAAG;AAC9B,mBAAS,cAAc,GAAU,CAAG;AACpC,mBAAS,MAAO;AAAA,QACjB;AAED,aAAK,OAAO,OAAO,QAAQ;AAE3B,aAAK;AAEL,YAAI,KAAK,mBAAmB,cAAc;AAAQ;AAAA,MACnD;AAED,UAAI,KAAK,OAAO;AAAiB,aAAK,OAAO,gBAAiB;AAAA,IAC/D;AAED,aAAS,cAAc,KAAK,YAAY,KAAK,UAAU;AACvD,UAAM,qBAAqB,KAAK,kBAAkB;AAElD,QAAI,qBAAqB,GAAG;AAC1B,WAAK,aAAa,SAAS,EAAE,QAAQ;AACrC,WAAK,aAAa,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AAC9D,eAAS,gBAAgB,WAAW;AACpC,eAAS,MAAO;AAChB,WAAK,OAAO,OAAO,QAAQ;AAAA,IAC5B;AAED,QAAI,qBAAqB,GAAK;AAC5B,WAAK,aAAa,SAAS,EAAE,QAAQ,IAAM;AAC3C,WAAK,aAAa,UAAU,EAAE,QAAQ,KAAK,iBAAiB;AAC5D,eAAS,gBAAgB,WAAW;AACpC,WAAK,OAAO,OAAO,QAAQ;AAAA,IAC5B;AAED,aAAS,YAAY;AACrB,aAAS,cAAc,KAAK,gBAAgB,aAAa;AAAA,EAC1D;AAAA,EAED,UAAU;AACR,UAAM,QAAS;AAEf,QAAI,KAAK,uBAAuB;AAAW,WAAK,mBAAmB,QAAS;AAC5E,QAAI,KAAK,qBAAqB;AAAW,WAAK,iBAAiB,QAAS;AAAA,EACzE;AACH;AAGA,MAAM,iBAAiB;AAAA,EACtB;AAAA,IACC,CAAE,GAAG,CAAG;AAAA,EACR;AAAA,EACD;AAAA,IACC,CAAE,GAAG,CAAG;AAAA,IAAE,CAAE,IAAK,EAAK;AAAA,EACtB;AAAA,EACD;AAAA,IACC,CAAE,IAAK,EAAG;AAAA,IAAI,CAAE,GAAG;IAAO,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,GAAG,CAAG;AAAA,EAC9C;AAAA,EACD;AAAA,IACC,CAAE,GAAG,EAAK;AAAA,IAAE,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,GAAG,CAAG;AAAA,IAAE,CAAE,IAAK,EAAK;AAAA,IAC9C,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,IAAK;IAAO,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,EAAK;AAAA,EAC9C;AAAA,EACD;AAAA,IACC,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,IAAK,EAAG;AAAA,IAAI,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,GAAG,EAAK;AAAA,IAC9C,CAAE,IAAK,EAAK;AAAA,IAAE,CAAE,GAAG;IAAK,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,EAAK;AAAA,IAC5C,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,IAAK,EAAG;AAAA,IAAI,CAAE,IAAK,CAAG;AAAA,IAChD,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,GAAG,CAAG;AAAA,IAAE,CAAE,IAAK,EAAK;AAAA,EAC9C;AAAA,EACD;AAAA,IACC,CAAE,IAAK,EAAK;AAAA,IAAE,CAAE,IAAK,EAAG;AAAA,IAAI,CAAE,IAAK,EAAG;AAAA,IAAI,CAAE,IAAK,EAAK;AAAA,IACtD,CAAE,IAAK,EAAK;AAAA,IAAE,CAAE,IAAK,EAAK;AAAA,IAAE,CAAE,IAAK,EAAK;AAAA,IAAE,CAAE,IAAK,CAAG;AAAA,IACpD,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,IAAK,CAAG;AAAA,IAC9C,CAAE,IAAK,CAAG;AAAA,IAAE,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,IAAK,CAAC;AAAA,IAAI,CAAE,IAAK,CAAG;AAAA,IAC9C,CAAE,GAAG,EAAK;AAAA,IAAE,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,GAAG,EAAK;AAAA,IAC9C,CAAE,GAAG,EAAK;AAAA,IAAE,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,GAAG,EAAG;AAAA,IAAI,CAAE,GAAG,EAAK;AAAA,IAC9C,CAAE,GAAG;IAAK,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,CAAG;AAAA,IACtC,CAAE,GAAG;IAAK,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,CAAC;AAAA,IAAI,CAAE,GAAG,CAAG;AAAA,EACtC;AACF;;"}