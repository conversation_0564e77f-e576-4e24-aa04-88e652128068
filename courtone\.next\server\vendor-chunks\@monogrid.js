"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@monogrid";
exports.ids = ["vendor-chunks/@monogrid"];
exports.modules = {

/***/ "(ssr)/./node_modules/@monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Q: () => (/* binding */ QuadRenderer)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\n\n\nconst getBufferForType = (type, width, height) => {\n    let out;\n    switch (type) {\n        case three__WEBPACK_IMPORTED_MODULE_0__.UnsignedByteType:\n            out = new Uint8ClampedArray(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType:\n            out = new Uint16Array(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.UnsignedIntType:\n            out = new Uint32Array(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.ByteType:\n            out = new Int8Array(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.ShortType:\n            out = new Int16Array(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.IntType:\n            out = new Int32Array(width * height * 4);\n            break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.FloatType:\n            out = new Float32Array(width * height * 4);\n            break;\n        default:\n            throw new Error('Unsupported data type');\n    }\n    return out;\n};\nlet _canReadPixelsResult;\n/**\n * Test if this browser implementation can correctly read pixels from the specified\n * Render target type.\n *\n * Runs only once\n *\n * @param type\n * @param renderer\n * @param camera\n * @param renderTargetOptions\n * @returns\n */\nconst canReadPixels = (type, renderer, camera, renderTargetOptions) => {\n    if (_canReadPixelsResult !== undefined)\n        return _canReadPixelsResult;\n    const testRT = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(1, 1, renderTargetOptions);\n    renderer.setRenderTarget(testRT);\n    const mesh = new three__WEBPACK_IMPORTED_MODULE_0__.Mesh(new three__WEBPACK_IMPORTED_MODULE_0__.PlaneGeometry(), new three__WEBPACK_IMPORTED_MODULE_0__.MeshBasicMaterial({ color: 0xffffff }));\n    renderer.render(mesh, camera);\n    renderer.setRenderTarget(null);\n    const out = getBufferForType(type, testRT.width, testRT.height);\n    renderer.readRenderTargetPixels(testRT, 0, 0, testRT.width, testRT.height, out);\n    testRT.dispose();\n    mesh.geometry.dispose();\n    mesh.material.dispose();\n    _canReadPixelsResult = out[0] !== 0;\n    return _canReadPixelsResult;\n};\n/**\n * Utility class used for rendering a texture with a material\n *\n * @category Core\n * @group Core\n */\nclass QuadRenderer {\n    /**\n     * Constructs a new QuadRenderer\n     *\n     * @param options Parameters for this QuadRenderer\n     */\n    constructor(options) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;\n        this._rendererIsDisposable = false;\n        this._supportsReadPixels = true;\n        /**\n         * Renders the input texture using the specified material\n         */\n        this.render = () => {\n            this._renderer.setRenderTarget(this._renderTarget);\n            try {\n                this._renderer.render(this._scene, this._camera);\n            }\n            catch (e) {\n                this._renderer.setRenderTarget(null);\n                throw e;\n            }\n            this._renderer.setRenderTarget(null);\n        };\n        this._width = options.width;\n        this._height = options.height;\n        this._type = options.type;\n        this._colorSpace = options.colorSpace;\n        const rtOptions = {\n            // fixed options\n            format: three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat,\n            depthBuffer: false,\n            stencilBuffer: false,\n            // user options\n            type: this._type, // set in class property\n            colorSpace: this._colorSpace, // set in class property\n            anisotropy: ((_a = options.renderTargetOptions) === null || _a === void 0 ? void 0 : _a.anisotropy) !== undefined ? (_b = options.renderTargetOptions) === null || _b === void 0 ? void 0 : _b.anisotropy : 1,\n            generateMipmaps: ((_c = options.renderTargetOptions) === null || _c === void 0 ? void 0 : _c.generateMipmaps) !== undefined ? (_d = options.renderTargetOptions) === null || _d === void 0 ? void 0 : _d.generateMipmaps : false,\n            magFilter: ((_e = options.renderTargetOptions) === null || _e === void 0 ? void 0 : _e.magFilter) !== undefined ? (_f = options.renderTargetOptions) === null || _f === void 0 ? void 0 : _f.magFilter : three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            minFilter: ((_g = options.renderTargetOptions) === null || _g === void 0 ? void 0 : _g.minFilter) !== undefined ? (_h = options.renderTargetOptions) === null || _h === void 0 ? void 0 : _h.minFilter : three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter,\n            samples: ((_j = options.renderTargetOptions) === null || _j === void 0 ? void 0 : _j.samples) !== undefined ? (_k = options.renderTargetOptions) === null || _k === void 0 ? void 0 : _k.samples : undefined,\n            wrapS: ((_l = options.renderTargetOptions) === null || _l === void 0 ? void 0 : _l.wrapS) !== undefined ? (_m = options.renderTargetOptions) === null || _m === void 0 ? void 0 : _m.wrapS : three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping,\n            wrapT: ((_o = options.renderTargetOptions) === null || _o === void 0 ? void 0 : _o.wrapT) !== undefined ? (_p = options.renderTargetOptions) === null || _p === void 0 ? void 0 : _p.wrapT : three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping\n        };\n        this._material = options.material;\n        if (options.renderer) {\n            this._renderer = options.renderer;\n        }\n        else {\n            this._renderer = QuadRenderer.instantiateRenderer();\n            this._rendererIsDisposable = true;\n        }\n        this._scene = new three__WEBPACK_IMPORTED_MODULE_0__.Scene();\n        this._camera = new three__WEBPACK_IMPORTED_MODULE_0__.OrthographicCamera();\n        this._camera.position.set(0, 0, 10);\n        this._camera.left = -0.5;\n        this._camera.right = 0.5;\n        this._camera.top = 0.5;\n        this._camera.bottom = -0.5;\n        this._camera.updateProjectionMatrix();\n        if (!canReadPixels(this._type, this._renderer, this._camera, rtOptions)) {\n            let alternativeType;\n            switch (this._type) {\n                case three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType:\n                    alternativeType = this._renderer.extensions.has('EXT_color_buffer_float') ? three__WEBPACK_IMPORTED_MODULE_0__.FloatType : undefined;\n                    break;\n            }\n            if (alternativeType !== undefined) {\n                console.warn(`This browser does not support reading pixels from ${this._type} RenderTargets, switching to ${three__WEBPACK_IMPORTED_MODULE_0__.FloatType}`);\n                this._type = alternativeType;\n            }\n            else {\n                this._supportsReadPixels = false;\n                console.warn('This browser dos not support toArray or toDataTexture, calls to those methods will result in an error thrown');\n            }\n        }\n        this._quad = new three__WEBPACK_IMPORTED_MODULE_0__.Mesh(new three__WEBPACK_IMPORTED_MODULE_0__.PlaneGeometry(), this._material);\n        this._quad.geometry.computeBoundingBox();\n        this._scene.add(this._quad);\n        this._renderTarget = new three__WEBPACK_IMPORTED_MODULE_0__.WebGLRenderTarget(this.width, this.height, rtOptions);\n        this._renderTarget.texture.mapping = ((_q = options.renderTargetOptions) === null || _q === void 0 ? void 0 : _q.mapping) !== undefined ? (_r = options.renderTargetOptions) === null || _r === void 0 ? void 0 : _r.mapping : three__WEBPACK_IMPORTED_MODULE_0__.UVMapping;\n    }\n    /**\n     * Instantiates a temporary renderer\n     *\n     * @returns\n     */\n    static instantiateRenderer() {\n        const renderer = new three__WEBPACK_IMPORTED_MODULE_1__.WebGLRenderer();\n        renderer.setSize(128, 128);\n        // renderer.outputColorSpace = SRGBColorSpace\n        // renderer.toneMapping = LinearToneMapping\n        // renderer.debug.checkShaderErrors = false\n        // this._rendererIsDisposable = true\n        return renderer;\n    }\n    /**\n     * Obtains a Buffer containing the rendered texture.\n     *\n     * @throws Error if the browser cannot read pixels from this RenderTarget type.\n     * @returns a TypedArray containing RGBA values from this renderer\n     */\n    toArray() {\n        if (!this._supportsReadPixels)\n            throw new Error('Can\\'t read pixels in this browser');\n        const out = getBufferForType(this._type, this._width, this._height);\n        this._renderer.readRenderTargetPixels(this._renderTarget, 0, 0, this._width, this._height, out);\n        return out;\n    }\n    /**\n     * Performs a readPixel operation in the renderTarget\n     * and returns a DataTexture containing the read data\n     *\n     * @param options options\n     * @returns\n     */\n    toDataTexture(options) {\n        const returnValue = new three__WEBPACK_IMPORTED_MODULE_0__.DataTexture(\n        // fixed values\n        this.toArray(), this.width, this.height, three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat, this._type, \n        // user values\n        (options === null || options === void 0 ? void 0 : options.mapping) || three__WEBPACK_IMPORTED_MODULE_0__.UVMapping, (options === null || options === void 0 ? void 0 : options.wrapS) || three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.wrapT) || three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.magFilter) || three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter, (options === null || options === void 0 ? void 0 : options.minFilter) || three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter, (options === null || options === void 0 ? void 0 : options.anisotropy) || 1, \n        // fixed value\n        three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace);\n        // set this afterwards, we can't set it in constructor\n        returnValue.generateMipmaps = (options === null || options === void 0 ? void 0 : options.generateMipmaps) !== undefined ? options === null || options === void 0 ? void 0 : options.generateMipmaps : false;\n        return returnValue;\n    }\n    /**\n     * If using a disposable renderer, it will dispose it.\n     */\n    disposeOnDemandRenderer() {\n        this._renderer.setRenderTarget(null);\n        if (this._rendererIsDisposable) {\n            this._renderer.dispose();\n            this._renderer.forceContextLoss();\n        }\n    }\n    /**\n     * Will dispose of **all** assets used by this renderer.\n     *\n     *\n     * @param disposeRenderTarget will dispose of the renderTarget which will not be usable later\n     * set this to true if you passed the `renderTarget.texture` to a `PMREMGenerator`\n     * or are otherwise done with it.\n     *\n     * @example\n     * ```js\n     * const loader = new HDRJPGLoader(renderer)\n     * const result = await loader.loadAsync('gainmap.jpeg')\n     * const mesh = new Mesh(geometry, new MeshBasicMaterial({ map: result.renderTarget.texture }) )\n     * // DO NOT dispose the renderTarget here,\n     * // it is used directly in the material\n     * result.dispose()\n     * ```\n     *\n     * @example\n     * ```js\n     * const loader = new HDRJPGLoader(renderer)\n     * const pmremGenerator = new PMREMGenerator( renderer );\n     * const result = await loader.loadAsync('gainmap.jpeg')\n     * const envMap = pmremGenerator.fromEquirectangular(result.renderTarget.texture)\n     * const mesh = new Mesh(geometry, new MeshStandardMaterial({ envMap }) )\n     * // renderTarget can be disposed here\n     * // because it was used to generate a PMREM texture\n     * result.dispose(true)\n     * ```\n     */\n    dispose(disposeRenderTarget) {\n        this.disposeOnDemandRenderer();\n        if (disposeRenderTarget) {\n            this.renderTarget.dispose();\n        }\n        // dispose shader material texture uniforms\n        if (this.material instanceof three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial) {\n            Object.values(this.material.uniforms).forEach(v => {\n                if (v.value instanceof three__WEBPACK_IMPORTED_MODULE_0__.Texture)\n                    v.value.dispose();\n            });\n        }\n        // dispose other material properties\n        Object.values(this.material).forEach(value => {\n            if (value instanceof three__WEBPACK_IMPORTED_MODULE_0__.Texture)\n                value.dispose();\n        });\n        this.material.dispose();\n        this._quad.geometry.dispose();\n    }\n    /**\n     * Width of the texture\n     */\n    get width() { return this._width; }\n    set width(value) {\n        this._width = value;\n        this._renderTarget.setSize(this._width, this._height);\n    }\n    /**\n     * Height of the texture\n     */\n    get height() { return this._height; }\n    set height(value) {\n        this._height = value;\n        this._renderTarget.setSize(this._width, this._height);\n    }\n    /**\n     * The renderer used\n     */\n    get renderer() { return this._renderer; }\n    /**\n     * The `WebGLRenderTarget` used.\n     */\n    get renderTarget() { return this._renderTarget; }\n    set renderTarget(value) {\n        this._renderTarget = value;\n        this._width = value.width;\n        this._height = value.height;\n        // this._type = value.texture.type\n    }\n    /**\n     * The `Material` used.\n     */\n    get material() { return this._material; }\n    /**\n     *\n     */\n    get type() { return this._type; }\n    get colorSpace() { return this._colorSpace; }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monogrid/gainmap-js/dist/decode.js":
/*!**********************************************************!*\
  !*** ./node_modules/@monogrid/gainmap-js/dist/decode.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GainMapDecoderMaterial: () => (/* binding */ GainMapDecoderMaterial),\n/* harmony export */   GainMapLoader: () => (/* binding */ GainMapLoader),\n/* harmony export */   HDRJPGLoader: () => (/* binding */ HDRJPGLoader),\n/* harmony export */   JPEGRLoader: () => (/* binding */ HDRJPGLoader),\n/* harmony export */   MPFExtractor: () => (/* binding */ MPFExtractor),\n/* harmony export */   QuadRenderer: () => (/* reexport safe */ _QuadRenderer_DuOPRGA4_js__WEBPACK_IMPORTED_MODULE_1__.Q),\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   extractGainmapFromJPEG: () => (/* binding */ extractGainmapFromJPEG),\n/* harmony export */   extractXMP: () => (/* binding */ extractXMP)\n/* harmony export */ });\n/* harmony import */ var _QuadRenderer_DuOPRGA4_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QuadRenderer-DuOPRGA4.js */ \"(ssr)/./node_modules/@monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\n\n\n\nconst vertexShader = /* glsl */ `\nvarying vec2 vUv;\n\nvoid main() {\n  vUv = uv;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}\n`;\nconst fragmentShader = /* glsl */ `\n// min half float value\n#define HALF_FLOAT_MIN vec3( -65504, -65504, -65504 )\n// max half float value\n#define HALF_FLOAT_MAX vec3( 65504, 65504, 65504 )\n\nuniform sampler2D sdr;\nuniform sampler2D gainMap;\nuniform vec3 gamma;\nuniform vec3 offsetHdr;\nuniform vec3 offsetSdr;\nuniform vec3 gainMapMin;\nuniform vec3 gainMapMax;\nuniform float weightFactor;\n\nvarying vec2 vUv;\n\nvoid main() {\n  vec3 rgb = texture2D( sdr, vUv ).rgb;\n  vec3 recovery = texture2D( gainMap, vUv ).rgb;\n  vec3 logRecovery = pow( recovery, gamma );\n  vec3 logBoost = gainMapMin * ( 1.0 - logRecovery ) + gainMapMax * logRecovery;\n  vec3 hdrColor = (rgb + offsetSdr) * exp2( logBoost * weightFactor ) - offsetHdr;\n  vec3 clampedHdrColor = max( HALF_FLOAT_MIN, min( HALF_FLOAT_MAX, hdrColor ));\n  gl_FragColor = vec4( clampedHdrColor , 1.0 );\n}\n`;\n/**\n * A Material which is able to decode the Gainmap into a full HDR Representation\n *\n * @category Materials\n * @group Materials\n */\nclass GainMapDecoderMaterial extends three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial {\n    /**\n     *\n     * @param params\n     */\n    constructor({ gamma, offsetHdr, offsetSdr, gainMapMin, gainMapMax, maxDisplayBoost, hdrCapacityMin, hdrCapacityMax, sdr, gainMap }) {\n        super({\n            name: 'GainMapDecoderMaterial',\n            vertexShader,\n            fragmentShader,\n            uniforms: {\n                sdr: { value: sdr },\n                gainMap: { value: gainMap },\n                gamma: { value: new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(1.0 / gamma[0], 1.0 / gamma[1], 1.0 / gamma[2]) },\n                offsetHdr: { value: new three__WEBPACK_IMPORTED_MODULE_0__.Vector3().fromArray(offsetHdr) },\n                offsetSdr: { value: new three__WEBPACK_IMPORTED_MODULE_0__.Vector3().fromArray(offsetSdr) },\n                gainMapMin: { value: new three__WEBPACK_IMPORTED_MODULE_0__.Vector3().fromArray(gainMapMin) },\n                gainMapMax: { value: new three__WEBPACK_IMPORTED_MODULE_0__.Vector3().fromArray(gainMapMax) },\n                weightFactor: {\n                    value: (Math.log2(maxDisplayBoost) - hdrCapacityMin) / (hdrCapacityMax - hdrCapacityMin)\n                }\n            },\n            blending: three__WEBPACK_IMPORTED_MODULE_0__.NoBlending,\n            depthTest: false,\n            depthWrite: false\n        });\n        this._maxDisplayBoost = maxDisplayBoost;\n        this._hdrCapacityMin = hdrCapacityMin;\n        this._hdrCapacityMax = hdrCapacityMax;\n        this.needsUpdate = true;\n        this.uniformsNeedUpdate = true;\n    }\n    get sdr() { return this.uniforms.sdr.value; }\n    set sdr(value) { this.uniforms.sdr.value = value; }\n    get gainMap() { return this.uniforms.gainMap.value; }\n    set gainMap(value) { this.uniforms.gainMap.value = value; }\n    /**\n     * @see {@link GainMapMetadata.offsetHdr}\n     */\n    get offsetHdr() { return this.uniforms.offsetHdr.value.toArray(); }\n    set offsetHdr(value) { this.uniforms.offsetHdr.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.offsetSdr}\n     */\n    get offsetSdr() { return this.uniforms.offsetSdr.value.toArray(); }\n    set offsetSdr(value) { this.uniforms.offsetSdr.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gainMapMin}\n     */\n    get gainMapMin() { return this.uniforms.gainMapMin.value.toArray(); }\n    set gainMapMin(value) { this.uniforms.gainMapMin.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gainMapMax}\n     */\n    get gainMapMax() { return this.uniforms.gainMapMax.value.toArray(); }\n    set gainMapMax(value) { this.uniforms.gainMapMax.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gamma}\n     */\n    get gamma() {\n        const g = this.uniforms.gamma.value;\n        return [1 / g.x, 1 / g.y, 1 / g.z];\n    }\n    set gamma(value) {\n        const g = this.uniforms.gamma.value;\n        g.x = 1.0 / value[0];\n        g.y = 1.0 / value[1];\n        g.z = 1.0 / value[2];\n    }\n    /**\n     * @see {@link GainMapMetadata.hdrCapacityMin}\n     * @remarks Logarithmic space\n     */\n    get hdrCapacityMin() { return this._hdrCapacityMin; }\n    set hdrCapacityMin(value) {\n        this._hdrCapacityMin = value;\n        this.calculateWeight();\n    }\n    /**\n     * @see {@link GainMapMetadata.hdrCapacityMin}\n     * @remarks Logarithmic space\n     */\n    get hdrCapacityMax() { return this._hdrCapacityMax; }\n    set hdrCapacityMax(value) {\n        this._hdrCapacityMax = value;\n        this.calculateWeight();\n    }\n    /**\n     * @see {@link GainmapDecodingParameters.maxDisplayBoost}\n     * @remarks Non Logarithmic space\n     */\n    get maxDisplayBoost() { return this._maxDisplayBoost; }\n    set maxDisplayBoost(value) {\n        this._maxDisplayBoost = Math.max(1, Math.min(65504, value));\n        this.calculateWeight();\n    }\n    calculateWeight() {\n        const val = (Math.log2(this._maxDisplayBoost) - this._hdrCapacityMin) / (this._hdrCapacityMax - this._hdrCapacityMin);\n        this.uniforms.weightFactor.value = Math.max(0, Math.min(1, val));\n    }\n}\n\n/**\n * Decodes a gain map using a WebGLRenderTarget\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @example\n * import { decode } from '@monogrid/gainmap-js'\n * import {\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   TextureLoader,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const textureLoader = new TextureLoader()\n *\n * // load SDR Representation\n * const sdr = await textureLoader.loadAsync('sdr.jpg')\n * // load Gain map recovery image\n * const gainMap = await textureLoader.loadAsync('gainmap.jpg')\n * // load metadata\n * const metadata = await (await fetch('metadata.json')).json()\n *\n * const result = await decode({\n *   sdr,\n *   gainMap,\n *   // this allows to use `result.renderTarget.texture` directly\n *   renderer,\n *   // this will restore the full HDR range\n *   maxDisplayBoost: Math.pow(2, metadata.hdrCapacityMax),\n *   ...metadata\n * })\n *\n * const scene = new Scene()\n * // `result` can be used to populate a Texture\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n * @param params\n * @returns\n * @throws {Error} if the WebGLRenderer fails to render the gain map\n */\nconst decode = (params) => {\n    const { sdr, gainMap, renderer } = params;\n    if (sdr.colorSpace !== three__WEBPACK_IMPORTED_MODULE_0__.SRGBColorSpace) {\n        console.warn('SDR Colorspace needs to be *SRGBColorSpace*, setting it automatically');\n        sdr.colorSpace = three__WEBPACK_IMPORTED_MODULE_0__.SRGBColorSpace;\n    }\n    sdr.needsUpdate = true;\n    if (gainMap.colorSpace !== three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace) {\n        console.warn('Gainmap Colorspace needs to be *LinearSRGBColorSpace*, setting it automatically');\n        gainMap.colorSpace = three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace;\n    }\n    gainMap.needsUpdate = true;\n    const material = new GainMapDecoderMaterial({\n        ...params,\n        sdr,\n        gainMap\n    });\n    const quadRenderer = new _QuadRenderer_DuOPRGA4_js__WEBPACK_IMPORTED_MODULE_1__.Q({\n        // TODO: three types are generic, eslint complains here, see how we can solve\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        width: sdr.image.width,\n        // TODO: three types are generic, eslint complains here, see how we can solve\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        height: sdr.image.height,\n        type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n        colorSpace: three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace,\n        material,\n        renderer,\n        renderTargetOptions: params.renderTargetOptions\n    });\n    try {\n        quadRenderer.render();\n    }\n    catch (e) {\n        quadRenderer.disposeOnDemandRenderer();\n        throw e;\n    }\n    return quadRenderer;\n};\n\nclass GainMapNotFoundError extends Error {\n}\n\nclass XMPMetadataNotFoundError extends Error {\n}\n\nconst getXMLValue = (xml, tag, defaultValue) => {\n    // Check for attribute format first: tag=\"value\"\n    const attributeMatch = new RegExp(`${tag}=\"([^\"]*)\"`, 'i').exec(xml);\n    if (attributeMatch)\n        return attributeMatch[1];\n    // Check for tag format: <tag>value</tag> or <tag><rdf:li>value</rdf:li>...</tag>\n    const tagMatch = new RegExp(`<${tag}[^>]*>([\\\\s\\\\S]*?)</${tag}>`, 'i').exec(xml);\n    if (tagMatch) {\n        // Check if it contains rdf:li elements\n        const liValues = tagMatch[1].match(/<rdf:li>([^<]*)<\\/rdf:li>/g);\n        if (liValues && liValues.length === 3) {\n            return liValues.map(v => v.replace(/<\\/?rdf:li>/g, ''));\n        }\n        return tagMatch[1].trim();\n    }\n    if (defaultValue !== undefined)\n        return defaultValue;\n    throw new Error(`Can't find ${tag} in gainmap metadata`);\n};\nconst extractXMP = (input) => {\n    let str;\n    // support node test environment\n    if (typeof TextDecoder !== 'undefined')\n        str = new TextDecoder().decode(input);\n    else\n        str = input.toString();\n    let start = str.indexOf('<x:xmpmeta');\n    while (start !== -1) {\n        const end = str.indexOf('x:xmpmeta>', start);\n        const xmpBlock = str.slice(start, end + 10);\n        try {\n            const gainMapMin = getXMLValue(xmpBlock, 'hdrgm:GainMapMin', '0');\n            const gainMapMax = getXMLValue(xmpBlock, 'hdrgm:GainMapMax');\n            const gamma = getXMLValue(xmpBlock, 'hdrgm:Gamma', '1');\n            const offsetSDR = getXMLValue(xmpBlock, 'hdrgm:OffsetSDR', '0.015625');\n            const offsetHDR = getXMLValue(xmpBlock, 'hdrgm:OffsetHDR', '0.015625');\n            // These are always attributes, so we can use a simpler regex\n            const hdrCapacityMinMatch = /hdrgm:HDRCapacityMin=\"([^\"]*)\"/.exec(xmpBlock);\n            const hdrCapacityMin = hdrCapacityMinMatch ? hdrCapacityMinMatch[1] : '0';\n            const hdrCapacityMaxMatch = /hdrgm:HDRCapacityMax=\"([^\"]*)\"/.exec(xmpBlock);\n            if (!hdrCapacityMaxMatch)\n                throw new Error('Incomplete gainmap metadata');\n            const hdrCapacityMax = hdrCapacityMaxMatch[1];\n            return {\n                gainMapMin: Array.isArray(gainMapMin) ? gainMapMin.map(v => parseFloat(v)) : [parseFloat(gainMapMin), parseFloat(gainMapMin), parseFloat(gainMapMin)],\n                gainMapMax: Array.isArray(gainMapMax) ? gainMapMax.map(v => parseFloat(v)) : [parseFloat(gainMapMax), parseFloat(gainMapMax), parseFloat(gainMapMax)],\n                gamma: Array.isArray(gamma) ? gamma.map(v => parseFloat(v)) : [parseFloat(gamma), parseFloat(gamma), parseFloat(gamma)],\n                offsetSdr: Array.isArray(offsetSDR) ? offsetSDR.map(v => parseFloat(v)) : [parseFloat(offsetSDR), parseFloat(offsetSDR), parseFloat(offsetSDR)],\n                offsetHdr: Array.isArray(offsetHDR) ? offsetHDR.map(v => parseFloat(v)) : [parseFloat(offsetHDR), parseFloat(offsetHDR), parseFloat(offsetHDR)],\n                hdrCapacityMin: parseFloat(hdrCapacityMin),\n                hdrCapacityMax: parseFloat(hdrCapacityMax)\n            };\n        }\n        catch (e) {\n            // Continue searching for another xmpmeta block if this one fails\n        }\n        start = str.indexOf('<x:xmpmeta', end);\n    }\n};\n\n/**\n * MPF Extractor (Multi Picture Format Extractor)\n * By Henrik S Nilsson 2019\n *\n * Extracts images stored in images based on the MPF format (found here: https://www.cipa.jp/e/std/std-sec.html\n * under \"CIPA DC-007-Translation-2021 Multi-Picture Format\"\n *\n * Overly commented, and without intention of being complete or production ready.\n * Created to extract depth maps from iPhone images, and to learn about image metadata.\n * Kudos to: Phil Harvey (exiftool), Jaume Sanchez (android-lens-blur-depth-extractor)\n */\nclass MPFExtractor {\n    constructor(options) {\n        this.options = {\n            debug: options && options.debug !== undefined ? options.debug : false,\n            extractFII: options && options.extractFII !== undefined ? options.extractFII : true,\n            extractNonFII: options && options.extractNonFII !== undefined ? options.extractNonFII : true\n        };\n    }\n    extract(imageArrayBuffer) {\n        return new Promise((resolve, reject) => {\n            const debug = this.options.debug;\n            const dataView = new DataView(imageArrayBuffer.buffer);\n            // If you're executing this line on a big endian machine, it'll be reversed.\n            // bigEnd further down though, refers to the endianness of the image itself.\n            if (dataView.getUint16(0) !== 0xffd8) {\n                reject(new Error('Not a valid jpeg'));\n                return;\n            }\n            const length = dataView.byteLength;\n            let offset = 2;\n            let loops = 0;\n            let marker; // APP# marker\n            while (offset < length) {\n                if (++loops > 250) {\n                    reject(new Error(`Found no marker after ${loops} loops 😵`));\n                    return;\n                }\n                if (dataView.getUint8(offset) !== 0xff) {\n                    reject(new Error(`Not a valid marker at offset 0x${offset.toString(16)}, found: 0x${dataView.getUint8(offset).toString(16)}`));\n                    return;\n                }\n                marker = dataView.getUint8(offset + 1);\n                if (debug)\n                    console.log(`Marker: ${marker.toString(16)}`);\n                if (marker === 0xe2) {\n                    if (debug)\n                        console.log('Found APP2 marker (0xffe2)');\n                    // Works for iPhone 8 Plus, X, and XSMax. Or any photos of MPF format.\n                    // Great way to visualize image information in html is using Exiftool. E.g.:\n                    // ./exiftool.exe -htmldump -wantTrailer photo.jpg > photo.html\n                    const formatPt = offset + 4;\n                    /*\n                     *  Structure of the MP Format Identifier\n                     *\n                     *  Offset Addr.  | Code (Hex)  | Description\n                     *  +00             ff            Marker Prefix      <-- offset\n                     *  +01             e2            APP2\n                     *  +02             #n            APP2 Field Length\n                     *  +03             #n            APP2 Field Length\n                     *  +04             4d            'M'                <-- formatPt\n                     *  +05             50            'P'\n                     *  +06             46            'F'\n                     *  +07             00            NULL\n                     *                                                   <-- tiffOffset\n                     */\n                    if (dataView.getUint32(formatPt) === 0x4d504600) {\n                        // Found MPF tag, so we start dig out sub images\n                        const tiffOffset = formatPt + 4;\n                        let bigEnd; // Endianness from TIFF header\n                        // Test for TIFF validity and endianness\n                        // 0x4949 and 0x4D4D ('II' and 'MM') marks Little Endian and Big Endian\n                        if (dataView.getUint16(tiffOffset) === 0x4949) {\n                            bigEnd = false;\n                        }\n                        else if (dataView.getUint16(tiffOffset) === 0x4d4d) {\n                            bigEnd = true;\n                        }\n                        else {\n                            reject(new Error('No valid endianness marker found in TIFF header'));\n                            return;\n                        }\n                        if (dataView.getUint16(tiffOffset + 2, !bigEnd) !== 0x002a) {\n                            reject(new Error('Not valid TIFF data! (no 0x002A marker)'));\n                            return;\n                        }\n                        // 32 bit number stating the offset from the start of the 8 Byte MP Header\n                        // to MP Index IFD Least possible value is thus 8 (means 0 offset)\n                        const firstIFDOffset = dataView.getUint32(tiffOffset + 4, !bigEnd);\n                        if (firstIFDOffset < 0x00000008) {\n                            reject(new Error('Not valid TIFF data! (First offset less than 8)'));\n                            return;\n                        }\n                        // Move ahead to MP Index IFD\n                        // Assume we're at the first IFD, so firstIFDOffset points to\n                        // MP Index IFD and not MP Attributes IFD. (If we try extract from a sub image,\n                        // we fail silently here due to this assumption)\n                        // Count (2 Byte) | MP Index Fields a.k.a. MP Entries (count * 12 Byte) | Offset of Next IFD (4 Byte)\n                        const dirStart = tiffOffset + firstIFDOffset; // Start of IFD (Image File Directory)\n                        const count = dataView.getUint16(dirStart, !bigEnd); // Count of MPEntries (2 Byte)\n                        // Extract info from MPEntries (starting after Count)\n                        const entriesStart = dirStart + 2;\n                        let numberOfImages = 0;\n                        for (let i = entriesStart; i < entriesStart + 12 * count; i += 12) {\n                            // Each entry is 12 Bytes long\n                            // Check MP Index IFD tags, here we only take tag 0xb001 = Number of images\n                            if (dataView.getUint16(i, !bigEnd) === 0xb001) {\n                                // stored in Last 4 bytes of its 12 Byte entry.\n                                numberOfImages = dataView.getUint32(i + 8, !bigEnd);\n                            }\n                        }\n                        const nextIFDOffsetLen = 4; // 4 Byte offset field that appears after MP Index IFD tags\n                        const MPImageListValPt = dirStart + 2 + count * 12 + nextIFDOffsetLen;\n                        const images = [];\n                        for (let i = MPImageListValPt; i < MPImageListValPt + numberOfImages * 16; i += 16) {\n                            const image = {\n                                MPType: dataView.getUint32(i, !bigEnd),\n                                size: dataView.getUint32(i + 4, !bigEnd),\n                                // This offset is specified relative to the address of the MP Endian\n                                // field in the MP Header, unless the image is a First Individual Image,\n                                // in which case the value of the offset shall be NULL (0x00000000).\n                                dataOffset: dataView.getUint32(i + 8, !bigEnd),\n                                dependantImages: dataView.getUint32(i + 12, !bigEnd),\n                                start: -1,\n                                end: -1,\n                                isFII: false\n                            };\n                            if (!image.dataOffset) {\n                                // dataOffset is 0x00000000 for First Individual Image\n                                image.start = 0;\n                                image.isFII = true;\n                            }\n                            else {\n                                image.start = tiffOffset + image.dataOffset;\n                                image.isFII = false;\n                            }\n                            image.end = image.start + image.size;\n                            images.push(image);\n                        }\n                        if (this.options.extractNonFII && images.length) {\n                            const bufferBlob = new Blob([dataView]);\n                            const imgs = [];\n                            for (const image of images) {\n                                if (image.isFII && !this.options.extractFII) {\n                                    continue; // Skip FII\n                                }\n                                const imageBlob = bufferBlob.slice(image.start, image.end + 1, 'image/jpeg');\n                                // we don't need this\n                                // const imageUrl = URL.createObjectURL(imageBlob)\n                                // image.img = document.createElement('img')\n                                // image.img.src = imageUrl\n                                imgs.push(imageBlob);\n                            }\n                            resolve(imgs);\n                        }\n                    }\n                }\n                offset += 2 + dataView.getUint16(offset + 2);\n            }\n        });\n    }\n}\n\n/**\n * Extracts XMP Metadata and the gain map recovery image\n * from a single JPEG file.\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @param jpegFile an `Uint8Array` containing and encoded JPEG file\n * @returns an sdr `Uint8Array` compressed in JPEG, a gainMap `Uint8Array` compressed in JPEG and the XMP parsed XMP metadata\n * @throws Error if XMP Metadata is not found\n * @throws Error if Gain map image is not found\n * @example\n * import { FileLoader } from 'three'\n * import { extractGainmapFromJPEG } from '@monogrid/gainmap-js'\n *\n * const jpegFile = await new FileLoader()\n *  .setResponseType('arraybuffer')\n *  .loadAsync('image.jpg')\n *\n * const { sdr, gainMap, metadata } = extractGainmapFromJPEG(jpegFile)\n */\nconst extractGainmapFromJPEG = async (jpegFile) => {\n    const metadata = extractXMP(jpegFile);\n    if (!metadata)\n        throw new XMPMetadataNotFoundError('Gain map XMP metadata not found');\n    const mpfExtractor = new MPFExtractor({ extractFII: true, extractNonFII: true });\n    const images = await mpfExtractor.extract(jpegFile);\n    if (images.length !== 2)\n        throw new GainMapNotFoundError('Gain map recovery image not found');\n    return {\n        sdr: new Uint8Array(await images[0].arrayBuffer()),\n        gainMap: new Uint8Array(await images[1].arrayBuffer()),\n        metadata\n    };\n};\n\n/**\n * private function, async get image from blob\n *\n * @param blob\n * @returns\n */\nconst getHTMLImageFromBlob = (blob) => {\n    return new Promise((resolve, reject) => {\n        const img = document.createElement('img');\n        img.onload = () => { resolve(img); };\n        img.onerror = (e) => { reject(e); };\n        img.src = URL.createObjectURL(blob);\n    });\n};\n\nclass LoaderBase extends three__WEBPACK_IMPORTED_MODULE_0__.Loader {\n    /**\n     *\n     * @param renderer\n     * @param manager\n     */\n    constructor(renderer, manager) {\n        super(manager);\n        if (renderer)\n            this._renderer = renderer;\n        this._internalLoadingManager = new three__WEBPACK_IMPORTED_MODULE_0__.LoadingManager();\n    }\n    /**\n     * Specify the renderer to use when rendering the gain map\n     *\n     * @param renderer\n     * @returns\n     */\n    setRenderer(renderer) {\n        this._renderer = renderer;\n        return this;\n    }\n    /**\n     * Specify the renderTarget options to use when rendering the gain map\n     *\n     * @param options\n     * @returns\n     */\n    setRenderTargetOptions(options) {\n        this._renderTargetOptions = options;\n        return this;\n    }\n    /**\n     * @private\n     * @returns\n     */\n    prepareQuadRenderer() {\n        if (!this._renderer)\n            console.warn('WARNING: An existing WebGL Renderer was not passed to this Loader constructor or in setRenderer, the result of this Loader will need to be converted to a Data Texture with toDataTexture() before you can use it in your renderer.');\n        // temporary values\n        const material = new GainMapDecoderMaterial({\n            gainMapMax: [1, 1, 1],\n            gainMapMin: [0, 0, 0],\n            gamma: [1, 1, 1],\n            offsetHdr: [1, 1, 1],\n            offsetSdr: [1, 1, 1],\n            hdrCapacityMax: 1,\n            hdrCapacityMin: 0,\n            maxDisplayBoost: 1,\n            gainMap: new three__WEBPACK_IMPORTED_MODULE_0__.Texture(),\n            sdr: new three__WEBPACK_IMPORTED_MODULE_0__.Texture()\n        });\n        return new _QuadRenderer_DuOPRGA4_js__WEBPACK_IMPORTED_MODULE_1__.Q({\n            width: 16,\n            height: 16,\n            type: three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType,\n            colorSpace: three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace,\n            material,\n            renderer: this._renderer,\n            renderTargetOptions: this._renderTargetOptions\n        });\n    }\n    /**\n   * @private\n   * @param quadRenderer\n   * @param metadata\n   * @param sdrBuffer\n   * @param gainMapBuffer\n   */\n    async render(quadRenderer, metadata, sdrBuffer, gainMapBuffer) {\n        // this is optional, will render a black gain-map if not present\n        const gainMapBlob = gainMapBuffer ? new Blob([gainMapBuffer], { type: 'image/jpeg' }) : undefined;\n        const sdrBlob = new Blob([sdrBuffer], { type: 'image/jpeg' });\n        let sdrImage;\n        let gainMapImage;\n        let needsFlip = false;\n        if (typeof createImageBitmap === 'undefined') {\n            const res = await Promise.all([\n                gainMapBlob ? getHTMLImageFromBlob(gainMapBlob) : Promise.resolve(undefined),\n                getHTMLImageFromBlob(sdrBlob)\n            ]);\n            gainMapImage = res[0];\n            sdrImage = res[1];\n            needsFlip = true;\n        }\n        else {\n            const res = await Promise.all([\n                gainMapBlob ? createImageBitmap(gainMapBlob, { imageOrientation: 'flipY' }) : Promise.resolve(undefined),\n                createImageBitmap(sdrBlob, { imageOrientation: 'flipY' })\n            ]);\n            gainMapImage = res[0];\n            sdrImage = res[1];\n        }\n        const gainMap = new three__WEBPACK_IMPORTED_MODULE_0__.Texture(gainMapImage || new ImageData(2, 2), three__WEBPACK_IMPORTED_MODULE_0__.UVMapping, three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter, three__WEBPACK_IMPORTED_MODULE_0__.LinearMipMapLinearFilter, three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat, three__WEBPACK_IMPORTED_MODULE_0__.UnsignedByteType, 1, three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace);\n        gainMap.flipY = needsFlip;\n        gainMap.needsUpdate = true;\n        const sdr = new three__WEBPACK_IMPORTED_MODULE_0__.Texture(sdrImage, three__WEBPACK_IMPORTED_MODULE_0__.UVMapping, three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, three__WEBPACK_IMPORTED_MODULE_0__.ClampToEdgeWrapping, three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter, three__WEBPACK_IMPORTED_MODULE_0__.LinearMipMapLinearFilter, three__WEBPACK_IMPORTED_MODULE_0__.RGBAFormat, three__WEBPACK_IMPORTED_MODULE_0__.UnsignedByteType, 1, three__WEBPACK_IMPORTED_MODULE_0__.SRGBColorSpace);\n        sdr.flipY = needsFlip;\n        sdr.needsUpdate = true;\n        quadRenderer.width = sdrImage.width;\n        quadRenderer.height = sdrImage.height;\n        quadRenderer.material.gainMap = gainMap;\n        quadRenderer.material.sdr = sdr;\n        quadRenderer.material.gainMapMin = metadata.gainMapMin;\n        quadRenderer.material.gainMapMax = metadata.gainMapMax;\n        quadRenderer.material.offsetHdr = metadata.offsetHdr;\n        quadRenderer.material.offsetSdr = metadata.offsetSdr;\n        quadRenderer.material.gamma = metadata.gamma;\n        quadRenderer.material.hdrCapacityMin = metadata.hdrCapacityMin;\n        quadRenderer.material.hdrCapacityMax = metadata.hdrCapacityMax;\n        quadRenderer.material.maxDisplayBoost = Math.pow(2, metadata.hdrCapacityMax);\n        quadRenderer.material.needsUpdate = true;\n        quadRenderer.render();\n    }\n}\n\n/**\n * A Three.js Loader for the gain map format.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { GainMapLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new GainMapLoader(renderer)\n *\n * const result = await loader.loadAsync(['sdr.jpeg', 'gainmap.jpeg', 'metadata.json'])\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass GainMapLoader extends LoaderBase {\n    /**\n     * Loads a gainmap using separate data\n     * * sdr image\n     * * gain map image\n     * * metadata json\n     *\n     * useful for webp gain maps\n     *\n     * @param urls An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n     * @param onLoad Load complete callback, will receive the result\n     * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n     * @param onError Error callback\n     * @returns\n     */\n    load([sdrUrl, gainMapUrl, metadataUrl], onLoad, onProgress, onError) {\n        const quadRenderer = this.prepareQuadRenderer();\n        let sdr;\n        let gainMap;\n        let metadata;\n        const loadCheck = async () => {\n            if (sdr && gainMap && metadata) {\n                // solves #16\n                try {\n                    await this.render(quadRenderer, metadata, sdr, gainMap);\n                }\n                catch (error) {\n                    this.manager.itemError(sdrUrl);\n                    this.manager.itemError(gainMapUrl);\n                    this.manager.itemError(metadataUrl);\n                    if (typeof onError === 'function')\n                        onError(error);\n                    quadRenderer.disposeOnDemandRenderer();\n                    return;\n                }\n                if (typeof onLoad === 'function')\n                    onLoad(quadRenderer);\n                this.manager.itemEnd(sdrUrl);\n                this.manager.itemEnd(gainMapUrl);\n                this.manager.itemEnd(metadataUrl);\n                quadRenderer.disposeOnDemandRenderer();\n            }\n        };\n        let sdrLengthComputable = true;\n        let sdrTotal = 0;\n        let sdrLoaded = 0;\n        let gainMapLengthComputable = true;\n        let gainMapTotal = 0;\n        let gainMapLoaded = 0;\n        let metadataLengthComputable = true;\n        let metadataTotal = 0;\n        let metadataLoaded = 0;\n        const progressHandler = () => {\n            if (typeof onProgress === 'function') {\n                const total = sdrTotal + gainMapTotal + metadataTotal;\n                const loaded = sdrLoaded + gainMapLoaded + metadataLoaded;\n                const lengthComputable = sdrLengthComputable && gainMapLengthComputable && metadataLengthComputable;\n                onProgress(new ProgressEvent('progress', { lengthComputable, loaded, total }));\n            }\n        };\n        this.manager.itemStart(sdrUrl);\n        this.manager.itemStart(gainMapUrl);\n        this.manager.itemStart(metadataUrl);\n        const sdrLoader = new three__WEBPACK_IMPORTED_MODULE_0__.FileLoader(this._internalLoadingManager);\n        sdrLoader.setResponseType('arraybuffer');\n        sdrLoader.setRequestHeader(this.requestHeader);\n        sdrLoader.setPath(this.path);\n        sdrLoader.setWithCredentials(this.withCredentials);\n        sdrLoader.load(sdrUrl, async (buffer) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof buffer === 'string')\n                throw new Error('Invalid sdr buffer');\n            sdr = buffer;\n            await loadCheck();\n        }, (e) => {\n            sdrLengthComputable = e.lengthComputable;\n            sdrLoaded = e.loaded;\n            sdrTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(sdrUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        const gainMapLoader = new three__WEBPACK_IMPORTED_MODULE_0__.FileLoader(this._internalLoadingManager);\n        gainMapLoader.setResponseType('arraybuffer');\n        gainMapLoader.setRequestHeader(this.requestHeader);\n        gainMapLoader.setPath(this.path);\n        gainMapLoader.setWithCredentials(this.withCredentials);\n        gainMapLoader.load(gainMapUrl, async (buffer) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof buffer === 'string')\n                throw new Error('Invalid gainmap buffer');\n            gainMap = buffer;\n            await loadCheck();\n        }, (e) => {\n            gainMapLengthComputable = e.lengthComputable;\n            gainMapLoaded = e.loaded;\n            gainMapTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(gainMapUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        const metadataLoader = new three__WEBPACK_IMPORTED_MODULE_0__.FileLoader(this._internalLoadingManager);\n        // metadataLoader.setResponseType('json')\n        metadataLoader.setRequestHeader(this.requestHeader);\n        metadataLoader.setPath(this.path);\n        metadataLoader.setWithCredentials(this.withCredentials);\n        metadataLoader.load(metadataUrl, async (json) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof json !== 'string')\n                throw new Error('Invalid metadata string');\n            // TODO: implement check on JSON file and remove this eslint disable\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            metadata = JSON.parse(json);\n            await loadCheck();\n        }, (e) => {\n            metadataLengthComputable = e.lengthComputable;\n            metadataLoaded = e.loaded;\n            metadataTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(metadataUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        return quadRenderer;\n    }\n}\n\n/**\n * A Three.js Loader for a JPEG with embedded gainmap metadata.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { HDRJPGLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new HDRJPGLoader(renderer)\n *\n * const result = await loader.loadAsync('gainmap.jpeg')\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass HDRJPGLoader extends LoaderBase {\n    /**\n     * Loads a JPEG containing gain map metadata\n     * Renders a normal SDR image if gainmap data is not found\n     *\n     * @param url An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n     * @param onLoad Load complete callback, will receive the result\n     * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n     * @param onError Error callback\n     * @returns\n     */\n    load(url, onLoad, onProgress, onError) {\n        const quadRenderer = this.prepareQuadRenderer();\n        const loader = new three__WEBPACK_IMPORTED_MODULE_0__.FileLoader(this._internalLoadingManager);\n        loader.setResponseType('arraybuffer');\n        loader.setRequestHeader(this.requestHeader);\n        loader.setPath(this.path);\n        loader.setWithCredentials(this.withCredentials);\n        this.manager.itemStart(url);\n        loader.load(url, async (jpeg) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof jpeg === 'string')\n                throw new Error('Invalid buffer, received [string], was expecting [ArrayBuffer]');\n            const jpegBuffer = new Uint8Array(jpeg);\n            let sdrJPEG;\n            let gainMapJPEG;\n            let metadata;\n            try {\n                const extractionResult = await extractGainmapFromJPEG(jpegBuffer);\n                // gain map is successfully reconstructed\n                sdrJPEG = extractionResult.sdr;\n                gainMapJPEG = extractionResult.gainMap;\n                metadata = extractionResult.metadata;\n            }\n            catch (e) {\n                // render the SDR version if this is not a gainmap\n                if (e instanceof XMPMetadataNotFoundError || e instanceof GainMapNotFoundError) {\n                    console.warn(`Failure to reconstruct an HDR image from ${url}: Gain map metadata not found in the file, HDRJPGLoader will render the SDR jpeg`);\n                    metadata = {\n                        gainMapMin: [0, 0, 0],\n                        gainMapMax: [1, 1, 1],\n                        gamma: [1, 1, 1],\n                        hdrCapacityMin: 0,\n                        hdrCapacityMax: 1,\n                        offsetHdr: [0, 0, 0],\n                        offsetSdr: [0, 0, 0]\n                    };\n                    sdrJPEG = jpegBuffer;\n                }\n                else {\n                    throw e;\n                }\n            }\n            // solves #16\n            try {\n                await this.render(quadRenderer, metadata, sdrJPEG, gainMapJPEG);\n            }\n            catch (error) {\n                this.manager.itemError(url);\n                if (typeof onError === 'function')\n                    onError(error);\n                quadRenderer.disposeOnDemandRenderer();\n                return;\n            }\n            if (typeof onLoad === 'function')\n                onLoad(quadRenderer);\n            this.manager.itemEnd(url);\n            quadRenderer.disposeOnDemandRenderer();\n        }, onProgress, (error) => {\n            this.manager.itemError(url);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        return quadRenderer;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monogrid/gainmap-js/dist/decode.js\n");

/***/ })

};
;